"""
命令行介面模組
提供用戶友好的命令行介面
"""

import click
import signal
import sys
from pathlib import Path

# 嘗試導入 rich，如果失敗則使用基本輸出
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    from rich.live import Live
    from rich.text import Text
    console = Console()
    HAS_RICH = True
except ImportError:
    console = None
    HAS_RICH = False


def print_message(message, style=None):
    """統一的輸出函數"""
    if HAS_RICH and console:
        if style:
            console.print(f"[{style}]{message}[/{style}]")
        else:
            console.print(message)
    else:
        print(message)


class LocalTranslateCLI:
    """本地翻譯 CLI 類"""
    
    def __init__(self):
        self.processor: StreamProcessor = None
        self.config_manager: ConfigManager = None
        self.is_running = False
    
    def setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            print_message("\n收到中斷信號，正在停止...", "yellow")
            self.stop_translation()
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def load_config(self, config_path: str):
        """載入配置"""
        try:
            # 延遲導入以避免依賴問題
            from .config_manager import ConfigManager
            self.config_manager = ConfigManager(config_path)
            print_message(f"✓ 配置文件載入成功: {config_path}", "green")
        except Exception as e:
            print_message(f"✗ 配置文件載入失敗: {e}", "red")
            raise
    
    def initialize_processor(self):
        """初始化處理器"""
        try:
            # 延遲導入以避免依賴問題
            from .stream_processor import StreamProcessor
            print_message("正在初始化處理器...", "blue")
            self.processor = StreamProcessor(self.config_manager)
            print_message("✓ 處理器初始化完成", "green")
        except Exception as e:
            print_message(f"✗ 處理器初始化失敗: {e}", "red")
            raise
    
    def start_translation(self):
        """開始翻譯"""
        if not self.processor:
            print_message("處理器未初始化", "red")
            return

        try:
            self.is_running = True
            self.processor.start_processing()

            # 顯示狀態面板
            self.show_status_panel()

            # 等待用戶中斷
            print_message("\n按 Ctrl+C 停止翻譯", "yellow")

            while self.is_running and self.processor.is_processing:
                try:
                    import time
                    time.sleep(0.1)
                except KeyboardInterrupt:
                    break

        except Exception as e:
            print_message(f"翻譯過程中發生錯誤: {e}", "red")
        finally:
            self.stop_translation()
    
    def stop_translation(self):
        """停止翻譯"""
        if self.processor:
            self.processor.stop_processing()
        self.is_running = False
    
    def show_status_panel(self):
        """顯示狀態面板"""
        if not self.processor:
            return

        status = self.processor.get_status()

        if HAS_RICH and console:
            # 創建狀態表格
            table = Table(title="系統狀態")
            table.add_column("組件", style="cyan")
            table.add_column("狀態", style="green")
            table.add_column("詳細信息", style="yellow")

            # 添加狀態行
            table.add_row(
                "處理器",
                "✓ 運行中" if status["is_processing"] else "✗ 停止",
                "即時翻譯處理"
            )

            table.add_row(
                "音頻捕獲",
                "✓ 活躍" if status["audio_capture_active"] else "✗ 停止",
                "麥克風音頻輸入"
            )

            if status["transcriber_info"]:
                transcriber_info = status["transcriber_info"]
                table.add_row(
                    "語音識別",
                    "✓ 就緒",
                    f"{transcriber_info['model_name']} ({transcriber_info['language']})"
                )

            if status["translator_info"]:
                translator_info = status["translator_info"]
                table.add_row(
                    "翻譯器",
                    "✓ 就緒",
                    f"{translator_info['source_language']} → {translator_info['target_language']}"
                )

            console.print(table)
        else:
            # 簡單文字輸出
            print("系統狀態:")
            print(f"  處理器: {'✓ 運行中' if status['is_processing'] else '✗ 停止'}")
            print(f"  音頻捕獲: {'✓ 活躍' if status['audio_capture_active'] else '✗ 停止'}")

            if status["transcriber_info"]:
                transcriber_info = status["transcriber_info"]
                print(f"  語音識別: ✓ 就緒 ({transcriber_info['model_name']})")

            if status["translator_info"]:
                translator_info = status["translator_info"]
                print(f"  翻譯器: ✓ 就緒 ({translator_info['source_language']} → {translator_info['target_language']})")
    
    def list_audio_devices(self):
        """列出音頻設備"""
        try:
            # 延遲導入以避免依賴問題
            from .config_manager import ConfigManager
            from .audio_capture import AudioCapture

            print_message("正在掃描音頻設備...", "blue")

            # 創建臨時音頻捕獲器來列出設備
            temp_config = ConfigManager()
            temp_audio = AudioCapture(temp_config)
            temp_audio.list_audio_devices()

        except Exception as e:
            print_message(f"無法列出音頻設備: {e}", "red")
    
    def test_components(self):
        """測試組件"""
        print_message("正在測試組件...", "blue")

        try:
            # 測試配置管理器
            print_message("測試配置管理器...")
            if self.config_manager:
                print_message("✓ 配置管理器正常", "green")
            else:
                print_message("✗ 配置管理器未初始化", "red")
                return

            # 測試處理器
            print_message("測試處理器...")
            if self.processor:
                status = self.processor.get_status()
                if status["components_initialized"]:
                    print_message("✓ 所有組件初始化完成", "green")
                else:
                    print_message("✗ 部分組件初始化失敗", "red")
            else:
                print_message("✗ 處理器未初始化", "red")

            print_message("組件測試完成", "green")

        except Exception as e:
            print_message(f"組件測試失敗: {e}", "red")


# CLI 命令定義
@click.group()
@click.option('--config', '-c', default='config.yaml', help='配置文件路徑')
@click.pass_context
def cli(ctx, config):
    """本地即時翻譯工具"""
    ctx.ensure_object(dict)
    ctx.obj['config_path'] = config

    # 顯示歡迎信息
    if HAS_RICH and console:
        console.print(Panel.fit(
            "[bold blue]本地即時翻譯工具[/bold blue]\n"
            "[dim]使用 Whisper + 本地 LLM 進行即時語音翻譯[/dim]",
            border_style="blue"
        ))
    else:
        print("=" * 50)
        print("本地即時翻譯工具")
        print("使用 Whisper + 本地 LLM 進行即時語音翻譯")
        print("=" * 50)


@cli.command()
@click.pass_context
def start(ctx):
    """開始即時翻譯"""
    config_path = ctx.obj['config_path']

    app = LocalTranslateCLI()
    app.setup_signal_handlers()

    try:
        app.load_config(config_path)
        app.initialize_processor()
        app.start_translation()
    except Exception as e:
        print_message(f"啟動失敗: {e}", "red")
        sys.exit(1)


@cli.command()
@click.pass_context
def devices(ctx):
    """列出可用的音頻設備"""
    config_path = ctx.obj['config_path']

    app = LocalTranslateCLI()
    try:
        app.load_config(config_path)
        app.list_audio_devices()
    except Exception as e:
        print_message(f"無法列出設備: {e}", "red")


@cli.command()
@click.pass_context
def test(ctx):
    """測試系統組件"""
    config_path = ctx.obj['config_path']

    app = LocalTranslateCLI()
    try:
        app.load_config(config_path)
        app.initialize_processor()
        app.test_components()
    except Exception as e:
        print_message(f"測試失敗: {e}", "red")


@cli.command()
@click.pass_context
def config_info(ctx):
    """顯示配置信息"""
    config_path = ctx.obj['config_path']

    try:
        # 延遲導入以避免依賴問題
        from .config_manager import ConfigManager
        config_manager = ConfigManager(config_path)

        if HAS_RICH and console:
            # 創建配置信息表格
            table = Table(title="配置信息")
            table.add_column("類別", style="cyan")
            table.add_column("設置", style="yellow")
            table.add_column("值", style="green")

            # 音頻配置
            audio_config = config_manager.get_audio_config()
            for key, value in audio_config.items():
                table.add_row("音頻", key, str(value))

            # Whisper 配置
            whisper_config = config_manager.get_whisper_config()
            for key, value in whisper_config.items():
                table.add_row("Whisper", key, str(value))

            # 翻譯配置
            translation_config = config_manager.get_translation_config()
            for key, value in translation_config.items():
                if key != "system_prompt":  # 系統提示太長，不顯示
                    table.add_row("翻譯", key, str(value))

            console.print(table)
        else:
            # 簡單文字輸出
            print("配置信息:")

            print("\n音頻配置:")
            audio_config = config_manager.get_audio_config()
            for key, value in audio_config.items():
                print(f"  {key}: {value}")

            print("\nWhisper 配置:")
            whisper_config = config_manager.get_whisper_config()
            for key, value in whisper_config.items():
                print(f"  {key}: {value}")

            print("\n翻譯配置:")
            translation_config = config_manager.get_translation_config()
            for key, value in translation_config.items():
                if key != "system_prompt":
                    print(f"  {key}: {value}")

    except Exception as e:
        print_message(f"無法讀取配置: {e}", "red")


if __name__ == '__main__':
    cli()
