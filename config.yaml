# 本地即時翻譯配置文件

# 音頻設置
audio:
  sample_rate: 16000
  chunk_size: 1024
  channels: 1
  device_index: null  # null 表示使用默認設備，系統音頻建議使用 0 (Microsoft 音效對應表)
  input_timeout: 1.0  # 音頻輸入超時時間（秒）
  capture_system_audio: false  # 設為 true 可捕獲系統音頻 (如 YouTube)

  # 系統音頻捕獲設置 (用於 YouTube 等應用)
  system_audio:
    # 推薦設備：0 (Microsoft 音效對應表) 或其他支援 loopback 的輸出設備
    device_index: 0  # Microsoft 音效對應表，適合捕獲系統音頻
    volume_threshold: 0.001  # 系統音頻音量閾值（較低以捕獲更多音頻）
    buffer_duration: 3.0  # 音頻緩衝時長（秒），較長可提高識別準確性

# Whisper 設置
whisper:
  model_name: "large-v3"
  language: "ja"  # 日語
  task: "transcribe"
  temperature: 0.0
  no_speech_threshold: 0.3
  condition_on_previous_text: true
  initial_prompt: "以下是日語對話的轉錄。"

# LLM 翻譯設置
llm:
  model_name: "facebook/nllb-200-distilled-600M"  # 多語言翻譯模型，支援日語到中文翻譯
  max_length: 512
  temperature: 0.3  # 降低溫度以提高翻譯一致性
  do_sample: false  # 關閉採樣以獲得更穩定的翻譯結果
  pad_token_id: 50256

  # 備用翻譯模型（按優先順序）
  fallback_models:
    - "facebook/nllb-200-distilled-600M"
    - "Helsinki-NLP/opus-mt-ja-zh"  # 如果可用
    - "simple_dict"  # 最後使用簡單字典翻譯
  
# 翻譯提示詞
translation:
  system_prompt: "你是一位專業翻譯員。請將日文字幕翻譯成正體中文。翻譯要自然流暢，貼近日常用語，保持原意。請勿添加任何解釋或註釋，只需提供翻譯結果。"
  source_language: "日語"
  target_language: "繁體中文"

  # NLLB 模型語言代碼
  nllb_source_lang: "jpn_Jpan"  # 日語
  nllb_target_lang: "zho_Hant"  # 繁體中文

# 輸出設置
output:
  show_timestamps: true
  timestamp_format: "%H:%M:%S,%f"  # 包含毫秒的時間格式
  show_original: true
  show_translation: true
  log_to_file: true  # 啟用日誌記錄
  log_file: "translation_log.txt"

  # 輸出格式設置
  format:
    # 時間戳格式：00:07:14,4 --> 00:07:19,8
    timestamp_separator: " --> "
    show_milliseconds: true
    line_separator: "----------------------------------------"

# 性能設置
performance:
  max_audio_length: 30  # 最大音頻長度（秒）
  processing_interval: 0.3  # 處理間隔（秒），較短以提高即時性
  buffer_size: 3  # 音頻緩衝區大小（秒），適合即時翻譯

  # 系統音頻專用性能設置
  system_audio:
    processing_interval: 0.2  # 系統音頻處理間隔更短
    min_audio_length: 1.0  # 最小音頻長度（秒）
    silence_threshold: 0.001  # 靜音閾值
