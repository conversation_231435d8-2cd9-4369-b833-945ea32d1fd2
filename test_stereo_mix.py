#!/usr/bin/env python3
"""
立體聲混音測試腳本
專門用於測試 Windows 立體聲混音功能
"""

import sys
import time
import numpy as np
from pathlib import Path

# 添加 src 目錄到 Python 路徑
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_stereo_mix():
    """測試立體聲混音功能"""
    print("=" * 60)
    print("Windows 立體聲混音測試")
    print("=" * 60)
    
    try:
        from src.config_manager import ConfigManager
        from src.audio_capture import AudioCapture
        
        print("1. 檢查音頻設備...")
        config = ConfigManager("config.yaml")
        audio_capture = AudioCapture(config)
        
        print("\n可用的輸入設備:")
        devices = __import__('sounddevice').query_devices()
        
        stereo_mix_found = False
        microsoft_sound_mapper = None
        
        for i, device in enumerate(devices):
            if device['max_input_channels'] > 0:
                device_name = device['name'].lower()
                if 'stereo mix' in device_name or '立體聲混音' in device_name:
                    print(f"  ✓ {i}: {device['name']} (立體聲混音) ⭐")
                    stereo_mix_found = True
                elif 'microsoft' in device_name and 'input' in device_name:
                    print(f"  ✓ {i}: {device['name']} (Microsoft 音效對應表)")
                    microsoft_sound_mapper = i
                else:
                    print(f"    {i}: {device['name']}")
        
        print("\n" + "=" * 60)
        
        if stereo_mix_found:
            print("✅ 找到立體聲混音設備！")
            print("這是捕獲系統音頻的最佳選擇。")
        elif microsoft_sound_mapper is not None:
            print("✅ 找到 Microsoft 音效對應表")
            print("可以嘗試使用此設備捕獲系統音頻。")
        else:
            print("⚠️  未找到立體聲混音設備")
            print("請按照以下步驟啟用立體聲混音：")
            print("1. 右鍵點擊音量圖標")
            print("2. 選擇「開啟音效設定」")
            print("3. 點擊「音效控制台」")
            print("4. 在「錄製」標籤中右鍵空白處")
            print("5. 選擇「顯示已停用的裝置」")
            print("6. 啟用「立體聲混音」")
        
        print("\n" + "=" * 60)
        print("2. 測試音頻捕獲")
        print("=" * 60)
        
        print("請確保：")
        print("- YouTube 影片正在播放")
        print("- 音量不是靜音")
        print("- 立體聲混音已啟用（如果有的話）")
        print()
        
        input("準備好後按 Enter 開始測試...")
        
        print("\n開始 10 秒音頻捕獲測試...")
        
        # 使用預設設備進行測試
        test_config = {
            'sample_rate': 16000,
            'chunk_size': 1024,
            'channels': 1,
            'device_index': None,  # 使用預設設備
            'input_timeout': 1.0,
            'capture_system_audio': False
        }
        
        class TestConfigManager:
            def get_audio_config(self):
                return test_config
        
        test_audio_capture = AudioCapture(TestConfigManager())
        
        try:
            test_audio_capture.start_recording()
            
            audio_detected = False
            max_volume = 0
            start_time = time.time()
            
            print("正在監聽音頻...")
            
            while time.time() - start_time < 10:
                try:
                    audio_chunk = test_audio_capture.audio_queue.get(timeout=0.5)
                    if len(audio_chunk) > 0:
                        volume = np.max(np.abs(audio_chunk))
                        max_volume = max(max_volume, volume)
                        
                        if volume > 0.001:
                            print(f"✓ 檢測到音頻! 音量: {volume:.4f}")
                            audio_detected = True
                        
                        # 顯示音量條
                        bar_length = int(volume * 50)
                        bar = "█" * bar_length + "░" * (50 - bar_length)
                        print(f"音量: [{bar}] {volume:.4f}", end="\r")
                
                except:
                    print(".", end="", flush=True)
                    continue
            
            test_audio_capture.stop_recording()
            
            print(f"\n\n測試結果:")
            print(f"最大音量: {max_volume:.4f}")
            
            if audio_detected:
                print("✅ 音頻捕獲測試成功!")
                print("您的設置可以用於 YouTube 翻譯")
                
                print("\n建議的配置:")
                print("config.yaml:")
                print("audio:")
                print("  device_index: null  # 使用預設設備")
                print("  capture_system_audio: false")
                
            else:
                print("❌ 未檢測到音頻")
                print("\n故障排除建議:")
                print("1. 確認 YouTube 正在播放且有聲音")
                print("2. 檢查 Windows 音效設定")
                print("3. 啟用立體聲混音功能")
                print("4. 確認預設錄製設備設置正確")
        
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
        
        print("\n" + "=" * 60)
        print("測試完成")
        print("=" * 60)
        
        if audio_detected:
            print("🎉 您可以開始使用翻譯工具了！")
            print("運行: python main.py start")
        else:
            print("📖 請參考 YOUTUBE_SETUP.md 獲取詳細設置指南")
        
    except Exception as e:
        print(f"測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_stereo_mix()
