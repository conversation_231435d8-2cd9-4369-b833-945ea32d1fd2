#!/usr/bin/env python3
"""
系統音頻捕獲測試腳本
"""

import sys
import time
import numpy as np
from pathlib import Path

# 添加 src 目錄到 Python 路徑
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_system_audio_capture():
    """測試系統音頻捕獲"""
    print("=" * 60)
    print("系統音頻捕獲測試")
    print("=" * 60)
    
    try:
        from src.config_manager import ConfigManager
        from src.audio_capture import AudioCapture
        
        # 顯示可用設備
        print("1. 顯示可用音頻設備:")
        print("-" * 40)
        config = ConfigManager("config.yaml")
        audio_capture = AudioCapture(config)
        audio_capture.list_audio_devices()
        
        print("\n" + "=" * 60)
        print("2. 系統音頻捕獲設置指南:")
        print("=" * 60)
        print("要捕獲 YouTube 等系統音頻，請按以下步驟操作:")
        print()
        print("步驟 1: 選擇輸出設備")
        print("從上面的輸出設備列表中，選擇一個標有 '(支援系統音頻捕獲)' 的設備")
        print("例如: 設備 12 或 13")
        print()
        print("步驟 2: 修改配置")
        print("編輯 config.yaml 文件，設置:")
        print("audio:")
        print("  device_index: 12  # 替換為您選擇的設備編號")
        print("  capture_system_audio: true")
        print()
        print("步驟 3: 啟動翻譯")
        print("運行: python main.py start")
        print()
        print("或者使用專門的系統音頻啟動腳本:")
        print("運行: start_system_audio.bat")
        
        print("\n" + "=" * 60)
        print("3. 快速測試 (可選)")
        print("=" * 60)
        
        device_choice = input("輸入要測試的輸出設備編號 (或按 Enter 跳過): ").strip()
        
        if device_choice.isdigit():
            device_index = int(device_choice)
            print(f"\n正在測試設備 {device_index} 的系統音頻捕獲...")
            print("請確保有音頻正在播放 (如 YouTube 影片)")
            print("測試將持續 10 秒...")
            
            # 創建測試配置
            test_config = {
                'sample_rate': 16000,
                'chunk_size': 1024,
                'channels': 1,
                'device_index': device_index,
                'input_timeout': 1.0,
                'capture_system_audio': True
            }
            
            class TestConfigManager:
                def get_audio_config(self):
                    return test_config
            
            test_audio_capture = AudioCapture(TestConfigManager())
            
            try:
                test_audio_capture.start_recording()
                
                audio_detected = False
                start_time = time.time()
                
                while time.time() - start_time < 10:  # 測試 10 秒
                    try:
                        for audio_chunk in test_audio_capture.get_audio_stream():
                            if len(audio_chunk) > 0:
                                volume = np.max(np.abs(audio_chunk))
                                if volume > 0.001:
                                    print(f"✓ 檢測到音頻! 音量: {volume:.4f}")
                                    audio_detected = True
                                    break
                            
                            if time.time() - start_time > 10:
                                break
                    except:
                        break
                
                test_audio_capture.stop_recording()
                
                if audio_detected:
                    print("✅ 系統音頻捕獲測試成功!")
                    print("您可以使用此設備進行 YouTube 翻譯")
                else:
                    print("⚠️  未檢測到音頻")
                    print("請確保:")
                    print("- 有音頻正在播放")
                    print("- 音量不是靜音")
                    print("- 選擇了正確的輸出設備")
                
            except Exception as e:
                print(f"❌ 測試失敗: {e}")
        
        print("\n" + "=" * 60)
        print("測試完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_system_audio_capture()
