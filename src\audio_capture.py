"""
音頻捕獲模組
負責即時音頻捕獲和處理
"""

import sounddevice as sd
import numpy as np
import threading
import queue
import time
from typing import Optional, Callable, Generator
from .config_manager import ConfigManager


class AudioCapture:
    """音頻捕獲器"""

    def __init__(self, config_manager: ConfigManager):
        """
        初始化音頻捕獲器

        Args:
            config_manager: 配置管理器
        """
        self.config = config_manager.get_audio_config()
        self.sample_rate = self.config.get('sample_rate', 16000)
        self.chunk_size = self.config.get('chunk_size', 1024)
        self.channels = self.config.get('channels', 1)
        self.device_index = self.config.get('device_index')
        self.input_timeout = self.config.get('input_timeout', 1.0)
        self.capture_system_audio = self.config.get('capture_system_audio', False)

        # 系統音頻專用設置
        self.system_audio_config = self.config.get('system_audio', {})
        self.system_device_index = self.system_audio_config.get('device_index', 0)
        self.system_volume_threshold = self.system_audio_config.get('volume_threshold', 0.001)
        self.system_buffer_duration = self.system_audio_config.get('buffer_duration', 3.0)

        self.stream = None
        self.audio_queue = queue.Queue()
        self.is_recording = False
        self.recording_thread: Optional[threading.Thread] = None
        
    def list_audio_devices(self):
        """列出可用的音頻設備"""
        print("可用的音頻設備:")
        devices = sd.query_devices()

        print("\n輸入設備 (麥克風等):")
        for i, device in enumerate(devices):
            if device['max_input_channels'] > 0:
                default_marker = " [默認]" if i == sd.default.device[0] else ""
                print(f"  {i}: {device['name']} (輸入通道: {device['max_input_channels']}){default_marker}")

        print("\n輸出設備 (揚聲器等，可用於系統音頻捕獲):")
        for i, device in enumerate(devices):
            if device['max_output_channels'] > 0:
                default_marker = " [默認]" if i == sd.default.device[1] else ""
                # 檢查是否支援 loopback (Windows WASAPI)
                hostapi_name = sd.query_hostapis(device['hostapi'])['name']
                loopback_info = " (支援系統音頻捕獲)" if hostapi_name == "Windows WASAPI" else ""
                print(f"  {i}: {device['name']} (輸出通道: {device['max_output_channels']}){default_marker}{loopback_info}")

        print("\n提示:")
        print("- 要捕獲麥克風音頻，請選擇輸入設備")
        print("- 要捕獲系統音頻 (如 YouTube)，請在 config.yaml 中設置:")
        print("  audio:")
        print("    device_index: <輸出設備編號>")
        print("    capture_system_audio: true")
    
    def start_recording(self):
        """開始錄音"""
        if self.is_recording:
            print("錄音已在進行中")
            return

        try:
            self.is_recording = True
            self.recording_thread = threading.Thread(target=self._record_audio)
            self.recording_thread.daemon = True
            self.recording_thread.start()

            audio_source = "系統音頻" if self.capture_system_audio else "麥克風音頻"
            print(f"開始錄音 (採樣率: {self.sample_rate}Hz, 通道: {self.channels}, 來源: {audio_source})")

        except Exception as e:
            print(f"無法開始錄音: {e}")
            self.is_recording = False
    
    def stop_recording(self):
        """停止錄音"""
        if not self.is_recording:
            return

        self.is_recording = False

        if self.recording_thread:
            self.recording_thread.join(timeout=2.0)

        if self.stream:
            self.stream.stop()
            self.stream = None

        print("錄音已停止")
    
    def _record_audio(self):
        """錄音線程函數"""
        def audio_callback(indata, frames, time, status):
            """音頻回調函數"""
            if status:
                print(f"音頻狀態: {status}")

            # 轉換為一維數組
            audio_data = indata[:, 0] if self.channels == 1 else indata.flatten()

            # 檢查音量，避免處理靜音
            if self.capture_system_audio:
                volume_threshold = self.system_volume_threshold
            else:
                volume_threshold = 0.01

            current_volume = np.max(np.abs(audio_data))
            if current_volume > volume_threshold:
                self.audio_queue.put(audio_data.copy())

        try:
            if self.capture_system_audio:
                # 系統音頻捕獲 (Windows WASAPI loopback)
                self._record_system_audio(audio_callback)
            else:
                # 麥克風音頻捕獲
                self._record_microphone_audio(audio_callback)
        except Exception as e:
            if self.is_recording:  # 只在仍在錄音時報告錯誤
                print(f"錄音錯誤: {e}")
            self.is_recording = False

    def _record_microphone_audio(self, audio_callback):
        """錄製麥克風音頻"""
        with sd.InputStream(
            samplerate=self.sample_rate,
            channels=self.channels,
            device=self.device_index,
            callback=audio_callback,
            blocksize=self.chunk_size,
            dtype=np.float32
        ) as stream:
            self.stream = stream
            while self.is_recording:
                time.sleep(0.1)

    def _record_system_audio(self, audio_callback):
        """錄製系統音頻 (Windows WASAPI loopback)"""
        try:
            import platform
            if platform.system() == "Windows":
                # 優先使用系統音頻配置中的設備
                target_device = self.system_device_index if self.system_device_index is not None else self.device_index

                # 如果沒有指定設備，嘗試找到 Microsoft 音效對應表
                if target_device is None:
                    target_device = self._find_microsoft_sound_mapper()

                print(f"嘗試使用設備 {target_device} 進行系統音頻捕獲")

                # 獲取設備信息
                device_info = sd.query_devices(target_device)
                print(f"設備信息: {device_info['name']}")

                # 檢查設備是否支援 WASAPI
                hostapi_info = sd.query_hostapis(device_info['hostapi'])
                if hostapi_info['name'] != "Windows WASAPI":
                    print(f"警告: 設備使用 {hostapi_info['name']}，建議使用 WASAPI 設備")

                # 嘗試使用 WASAPI loopback
                try:
                    with sd.InputStream(
                        samplerate=self.sample_rate,
                        channels=self.channels,
                        device=target_device,
                        callback=audio_callback,
                        blocksize=self.chunk_size,
                        dtype=np.float32,
                        extra_settings=sd.WasapiSettings(exclusive=False, auto_convert=True)
                    ) as stream:
                        self.stream = stream
                        print(f"✓ 成功使用 WASAPI loopback 模式捕獲系統音頻 (設備 {target_device})")
                        while self.is_recording:
                            time.sleep(0.1)
                except Exception as wasapi_error:
                    print(f"WASAPI loopback 失敗: {wasapi_error}")
                    # 嘗試不使用 WASAPI 設置
                    with sd.InputStream(
                        samplerate=self.sample_rate,
                        channels=self.channels,
                        device=target_device,
                        callback=audio_callback,
                        blocksize=self.chunk_size,
                        dtype=np.float32
                    ) as stream:
                        self.stream = stream
                        print(f"✓ 使用標準模式捕獲音頻 (設備 {target_device})")
                        while self.is_recording:
                            time.sleep(0.1)
            else:
                # 非 Windows 系統的處理
                print("警告: 系統音頻捕獲主要支援 Windows 系統")
                self._record_microphone_audio(audio_callback)
        except Exception as e:
            print(f"系統音頻捕獲失敗: {e}")
            print("嘗試使用麥克風模式...")
            self._record_microphone_audio(audio_callback)

    def _find_microsoft_sound_mapper(self):
        """尋找 Microsoft 音效對應表設備"""
        try:
            devices = sd.query_devices()
            for i, device in enumerate(devices):
                if "Microsoft 音效對應表" in device['name'] or "Microsoft Sound Mapper" in device['name']:
                    print(f"找到 Microsoft 音效對應表: 設備 {i}")
                    return i

            # 如果找不到，返回設備 0
            print("未找到 Microsoft 音效對應表，使用設備 0")
            return 0
        except Exception as e:
            print(f"搜尋設備時發生錯誤: {e}")
            return 0
    
    def get_audio_stream(self) -> Generator[np.ndarray, None, None]:
        """
        獲取音頻流生成器

        Yields:
            音頻數據塊
        """
        buffer = []
        buffer_duration = 0.0

        # 根據是否為系統音頻調整目標時長
        if self.capture_system_audio:
            target_duration = self.system_buffer_duration
            min_duration = 1.0  # 系統音頻最小處理時長
        else:
            target_duration = 2.0  # 麥克風音頻目標時長
            min_duration = 0.5  # 麥克風音頻最小處理時長

        while self.is_recording:
            try:
                # 獲取音頻數據，設置超時避免阻塞
                audio_chunk = self.audio_queue.get(timeout=self.input_timeout)
                buffer.extend(audio_chunk)

                # 計算當前緩衝時長
                buffer_duration = len(buffer) / self.sample_rate

                # 當緩衝達到目標時長時，返回數據
                if buffer_duration >= target_duration:
                    yield np.array(buffer, dtype=np.float32)
                    buffer = []
                    buffer_duration = 0.0

            except queue.Empty:
                # 超時時檢查是否有數據需要處理
                if buffer and buffer_duration > min_duration:
                    yield np.array(buffer, dtype=np.float32)
                    buffer = []
                    buffer_duration = 0.0
                continue
            except Exception as e:
                print(f"音頻流錯誤: {e}")
                break

        # 處理剩餘的緩衝數據
        if buffer and buffer_duration > 0.3:
            yield np.array(buffer, dtype=np.float32)
        
        # 處理剩餘的緩衝數據
        if buffer:
            yield np.array(buffer, dtype=np.float32)
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_recording()
