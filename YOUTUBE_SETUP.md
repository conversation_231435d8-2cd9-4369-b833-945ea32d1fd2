# YouTube 音頻捕獲設置指南

## 🎯 問題說明

您遇到的問題是：播放 YouTube 影片時，翻譯工具沒有捕獲到音頻內容。這是因為預設情況下，工具只捕獲麥克風音頻，而不是系統播放的音頻。

## 🔧 解決方案

### 方法一：啟用 Windows 立體聲混音 (推薦)

#### 步驟 1：啟用立體聲混音
1. 右鍵點擊系統托盤的 🔊 音量圖標
2. 選擇「開啟音效設定」
3. 點擊「音效控制台」或「更多音效設定」
4. 切換到「錄製」標籤
5. 在空白處右鍵，選擇「顯示已停用的裝置」
6. 找到「立體聲混音」或「Stereo Mix」
7. 右鍵點擊「立體聲混音」，選擇「啟用」
8. 再次右鍵，選擇「設為預設裝置」

#### 步驟 2：修改配置文件
編輯 `config.yaml` 文件：

```yaml
audio:
  device_index: 0  # Microsoft 音效對應表
  capture_system_audio: false  # 使用立體聲混音時保持 false
```

#### 步驟 3：測試
```bash
# 運行設備列表，確認立體聲混音已啟用
venv\Scripts\python.exe main.py devices

# 開始翻譯
venv\Scripts\python.exe main.py start
```

### 方法二：使用專門的系統音頻捕獲

#### 步驟 1：查看可用設備
```bash
venv\Scripts\python.exe main.py devices
```

#### 步驟 2：選擇輸出設備
從設備列表中找到標有「(支援系統音頻捕獲)」的設備，記下編號。

#### 步驟 3：修改配置
編輯 `config.yaml`：

```yaml
audio:
  device_index: 12  # 替換為您的輸出設備編號
  capture_system_audio: true
```

#### 步驟 4：啟動翻譯
```bash
venv\Scripts\python.exe main.py start
```

### 方法三：使用自動化腳本

運行專門的系統音頻啟動腳本：

```bash
start_system_audio.bat
```

這個腳本會：
1. 自動顯示可用設備
2. 讓您選擇合適的設備
3. 自動配置系統音頻捕獲
4. 啟動翻譯工具

## 🧪 測試步驟

1. **準備測試環境**：
   - 打開 YouTube 影片
   - 確保音量不是靜音
   - 確保有日語音頻內容

2. **啟動翻譯工具**：
   ```bash
   venv\Scripts\python.exe main.py start
   ```

3. **檢查輸出**：
   - 應該看到系統狀態顯示「✓ 活躍」
   - 當有音頻時，會顯示翻譯結果

4. **預期輸出格式**：
   ```
   [14:30:25] 00:00:01,500 --> 00:00:03,200
   原文: こんにちは、元気ですか？
   翻譯: 你好，你好嗎？
   ----------------------------------------
   ```

## 🔍 故障排除

### 問題 1：沒有檢測到音頻
**可能原因**：
- 立體聲混音未啟用
- 選擇了錯誤的音頻設備
- YouTube 音量太低

**解決方法**：
1. 確認立體聲混音已啟用並設為預設
2. 檢查 YouTube 音量
3. 嘗試不同的設備編號

### 問題 2：音頻品質差
**可能原因**：
- 音頻設備設置不當
- 採樣率不匹配

**解決方法**：
1. 在 `config.yaml` 中調整：
   ```yaml
   audio:
     sample_rate: 44100  # 嘗試更高的採樣率
     chunk_size: 2048    # 增加緩衝區大小
   ```

### 問題 3：翻譯延遲
**可能原因**：
- 處理間隔太長
- 緩衝區太大

**解決方法**：
1. 調整性能設置：
   ```yaml
   performance:
     processing_interval: 0.5  # 減少處理間隔
     buffer_size: 2           # 減少緩衝區大小
   ```

## 📋 快速檢查清單

- [ ] Windows 立體聲混音已啟用
- [ ] 立體聲混音設為預設錄製設備
- [ ] YouTube 影片正在播放且有聲音
- [ ] 配置文件設置正確
- [ ] 翻譯工具正常啟動
- [ ] 系統狀態顯示「✓ 活躍」

## 🎉 成功標誌

當設置正確時，您應該看到：

1. **啟動時**：
   ```
   ✓ 音頻捕獲器初始化完成
   開始錄音 (採樣率: 16000Hz, 通道: 1, 來源: 系統音頻)
   ```

2. **運行時**：
   ```
   [14:30:25] 00:00:01,500 --> 00:00:03,200
   原文: [日語文字]
   翻譯: [中文翻譯]
   ----------------------------------------
   ```

如果仍有問題，請檢查：
- Windows 音效設定
- 音頻驅動程式
- 防毒軟體是否阻擋音頻存取
