#!/usr/bin/env python3
"""
安裝檢查腳本
檢查所有依賴是否正確安裝
"""

import sys
import importlib
from pathlib import Path

def check_python_version():
    """檢查 Python 版本"""
    print("檢查 Python 版本...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - 版本符合要求")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - 需要 Python 3.8 或更高版本")
        return False

def check_package(package_name, import_name=None):
    """檢查包是否安裝"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✓ {package_name} - 已安裝")
        return True
    except ImportError:
        print(f"✗ {package_name} - 未安裝")
        return False

def check_files():
    """檢查必要文件是否存在"""
    print("\n檢查必要文件...")
    files_to_check = [
        "config.yaml",
        "requirements.txt",
        "main.py",
        "src/__init__.py",
        "src/config_manager.py",
        "src/audio_capture.py",
        "src/whisper_transcriber.py",
        "src/llm_translator.py",
        "src/stream_processor.py",
        "src/cli.py"
    ]
    
    all_files_exist = True
    for file_path in files_to_check:
        if Path(file_path).exists():
            print(f"✓ {file_path} - 存在")
        else:
            print(f"✗ {file_path} - 不存在")
            all_files_exist = False
    
    return all_files_exist

def main():
    """主函數"""
    print("=" * 50)
    print("本地即時翻譯工具 - 安裝檢查")
    print("=" * 50)
    
    # 檢查 Python 版本
    python_ok = check_python_version()
    
    print("\n檢查核心依賴包...")
    
    # 檢查核心包
    packages_to_check = [
        ("numpy", "numpy"),
        ("sounddevice", "sounddevice"),
        ("OpenAI Whisper", "whisper"),
        ("PyTorch", "torch"),
        ("Transformers", "transformers"),
        ("PyYAML", "yaml"),
        ("Click", "click"),
        ("Rich", "rich"),
    ]
    
    packages_ok = True
    for package_name, import_name in packages_to_check:
        if not check_package(package_name, import_name):
            packages_ok = False
    
    # 檢查文件
    files_ok = check_files()
    
    # 總結
    print("\n" + "=" * 50)
    print("檢查結果總結:")
    print("=" * 50)
    
    if python_ok:
        print("✓ Python 版本符合要求")
    else:
        print("✗ Python 版本不符合要求")
    
    if packages_ok:
        print("✓ 所有依賴包已安裝")
    else:
        print("✗ 部分依賴包未安裝")
        print("  請運行: pip install -r requirements.txt")
    
    if files_ok:
        print("✓ 所有必要文件存在")
    else:
        print("✗ 部分必要文件缺失")
    
    if python_ok and packages_ok and files_ok:
        print("\n🎉 安裝檢查通過！可以開始使用本地即時翻譯工具")
        print("運行命令: python main.py start")
    else:
        print("\n❌ 安裝檢查未通過，請解決上述問題後重試")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
