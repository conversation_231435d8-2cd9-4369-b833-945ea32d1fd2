#!/usr/bin/env python3
"""
YouTube 翻譯功能測試腳本
"""

import sys
import time
from pathlib import Path

# 添加 src 目錄到 Python 路徑
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_translation_functionality():
    """測試翻譯功能"""
    print("=" * 60)
    print("YouTube 翻譯功能測試")
    print("=" * 60)
    
    try:
        from src.config_manager import ConfigManager
        from src.llm_translator import LLMTranslator
        
        # 使用 YouTube 配置
        print("1. 載入 YouTube 配置...")
        config = ConfigManager("config_youtube.yaml")
        print("✓ 配置載入成功")
        
        # 初始化翻譯器
        print("\n2. 初始化翻譯器...")
        translator = LLMTranslator(config)
        print("✓ 翻譯器初始化完成")
        
        # 測試翻譯
        print("\n3. 測試翻譯功能...")
        test_texts = [
            "こんにちは、元気ですか？",
            "見たいみんなの猫の顔流れてるやつ見たいから送ってよ",
            "今日はいい天気ですね",
            "ありがとうございます",
            "お疲れ様でした",
            "日本の伝統的な料理を紹介します"
        ]
        
        print("測試文本翻譯結果：")
        print("-" * 40)
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n測試 {i}:")
            print(f"原文: {text}")
            
            start_time = time.time()
            translation = translator.translate_text(text)
            end_time = time.time()
            
            print(f"翻譯: {translation}")
            print(f"耗時: {end_time - start_time:.2f} 秒")
            print("-" * 40)
        
        # 顯示模型信息
        print("\n4. 翻譯器信息:")
        model_info = translator.get_model_info()
        for key, value in model_info.items():
            print(f"  {key}: {value}")
        
        print("\n" + "=" * 60)
        print("✅ 翻譯功能測試完成！")
        print("=" * 60)
        
        print("\n測試結果總結:")
        print("- 翻譯器載入: ✓")
        print("- 日語到繁體中文翻譯: ✓")
        print("- 翻譯速度: 正常")
        print("- 翻譯品質: 請檢查上述結果")
        
        print("\n🎉 您的 YouTube 翻譯系統已準備就緒！")
        print("可以使用以下命令啟動:")
        print("- Windows: start_youtube.bat")
        print("- Linux/macOS: ./start_youtube.sh")
        print("- 手動: python main.py -c config_youtube.yaml start")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n故障排除建議:")
        print("1. 確保所有依賴已安裝: pip install -r requirements.txt")
        print("2. 檢查網路連接（首次下載模型需要）")
        print("3. 確保有足夠的磁碟空間和記憶體")

def test_output_format():
    """測試輸出格式"""
    print("\n" + "=" * 60)
    print("輸出格式測試")
    print("=" * 60)
    
    try:
        from src.config_manager import ConfigManager
        from src.stream_processor import StreamProcessor
        
        config = ConfigManager("config_youtube.yaml")
        processor = StreamProcessor(config)
        
        # 模擬輸出
        print("\n模擬翻譯輸出格式:")
        print("=" * 40)
        
        # 測試輸出格式
        processor._output_result(
            "見たいみんなの猫の顔流れてるやつ見たいから送ってよ",
            "我想看大家貓咪的臉部表情，請傳給我看",
            434.4,  # 7分14.4秒
            439.8   # 7分19.8秒
        )
        
        print("✓ 輸出格式測試完成")
        
    except Exception as e:
        print(f"❌ 輸出格式測試失敗: {e}")

if __name__ == "__main__":
    test_translation_functionality()
    test_output_format()
    
    print("\n" + "=" * 60)
    print("所有測試完成")
    print("=" * 60)
