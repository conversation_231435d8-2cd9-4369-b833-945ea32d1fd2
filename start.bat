@echo off
setlocal enabledelayedexpansion
REM Local Real-time Translation Tool Launch Script (Windows)
REM This script will automatically set up the environment and start the translation tool

echo ========================================
echo Local Real-time Translation Tool
echo ========================================

REM Show current directory
echo Current directory: %CD%

REM Check Python installation
echo Checking Python installation...
python --version
if errorlevel 1 (
    echo.
    echo ERROR: Python not found, please install Python 3.8 or higher
    echo Download from https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
) else (
    echo OK: Python is installed
)

echo.
REM Check/Create virtual environment
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo.
        echo ERROR: Cannot create virtual environment
        echo Please ensure sufficient disk space and permissions
        echo.
        pause
        exit /b 1
    )
    echo OK: Virtual environment created
) else (
    echo OK: Virtual environment exists
)

echo.
REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo.
    echo ERROR: Cannot activate virtual environment
    echo Please check if venv\Scripts\activate.bat exists
    echo.
    pause
    exit /b 1
)
echo OK: Virtual environment activated

echo.
REM Upgrade pip
echo Upgrading pip...
pip install --upgrade pip
if errorlevel 1 (
    echo Warning: pip upgrade failed, continuing...
)

echo.
REM Install dependencies if needed
if not exist "venv\installed.flag" (
    echo Installing dependencies...
    echo This may take several minutes, please wait...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo.
        echo ERROR: Dependencies installation failed
        echo Please check internet connection and requirements.txt file
        echo You can also try manual installation: pip install -r requirements.txt
        echo.
        pause
        exit /b 1
    )
    echo. > venv\installed.flag
    echo OK: Dependencies installed
) else (
    echo OK: Dependencies already installed
)

echo.
REM Check configuration file
if not exist "config.yaml" (
    echo.
    echo ERROR: Configuration file config.yaml not found
    echo Please ensure the configuration file exists in current directory: %CD%
    echo.
    pause
    exit /b 1
)
echo OK: Configuration file exists

echo.
REM Run installation check
echo Running installation check...
python install_check.py
if errorlevel 1 (
    echo.
    echo Installation check failed, please resolve the issues above
    echo You can manually run: python install_check.py
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Ready to start translation tool
echo ========================================
echo Tip: Press Ctrl+C to stop translation
echo.

REM Start translation tool
python main.py start

echo.
echo ========================================
echo Translation tool stopped
echo ========================================

REM Keep window open
pause
