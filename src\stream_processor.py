"""
串流處理模組
負責協調音頻捕獲、語音識別和翻譯的整個流程
"""

import time
import threading
from datetime import datetime
from typing import Optional, Callable, Dict, Any
from .config_manager import ConfigManager
from .audio_capture import AudioCapture
from .whisper_transcriber import WhisperTranscriber
from .llm_translator import LLMTranslator


class StreamProcessor:
    """串流處理器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化串流處理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.output_config = config_manager.get_output_config()
        self.performance_config = config_manager.get_performance_config()
        
        # 輸出設置
        self.show_timestamps = self.output_config.get('show_timestamps', True)
        self.timestamp_format = self.output_config.get('timestamp_format', '%H:%M:%S,%f')
        self.show_original = self.output_config.get('show_original', True)
        self.show_translation = self.output_config.get('show_translation', True)
        self.log_to_file = self.output_config.get('log_to_file', False)
        self.log_file = self.output_config.get('log_file', 'translation_log.txt')

        # 輸出格式設置
        self.format_config = self.output_config.get('format', {})
        self.timestamp_separator = self.format_config.get('timestamp_separator', ' --> ')
        self.show_milliseconds = self.format_config.get('show_milliseconds', True)
        self.line_separator = self.format_config.get('line_separator', '----------------------------------------')
        
        # 性能設置
        self.processing_interval = self.performance_config.get('processing_interval', 0.5)
        
        # 組件初始化
        self.audio_capture: Optional[AudioCapture] = None
        self.transcriber: Optional[WhisperTranscriber] = None
        self.translator: Optional[LLMTranslator] = None
        
        # 處理狀態
        self.is_processing = False
        self.processing_thread: Optional[threading.Thread] = None

        # 回調函數
        self.on_transcription: Optional[Callable[[str, float, float], None]] = None
        self.on_translation: Optional[Callable[[str, str, float, float], None]] = None

        # 防止重複輸出
        self.last_output_text = ""
        self.last_output_time = 0.0
        self.processed_segments = set()  # 記錄已處理的分段

        self._initialize_components()
    
    def _initialize_components(self):
        """初始化所有組件"""
        try:
            print("正在初始化組件...")
            
            # 初始化音頻捕獲
            self.audio_capture = AudioCapture(self.config_manager)
            print("✓ 音頻捕獲器初始化完成")
            
            # 初始化語音識別
            self.transcriber = WhisperTranscriber(self.config_manager)
            print("✓ 語音識別器初始化完成")
            
            # 初始化翻譯器
            self.translator = LLMTranslator(self.config_manager)
            print("✓ 翻譯器初始化完成")
            
            print("所有組件初始化完成！")
            
        except Exception as e:
            print(f"組件初始化失敗: {e}")
            raise
    
    def start_processing(self):
        """開始串流處理"""
        if self.is_processing:
            print("處理已在進行中")
            return
        
        if not all([self.audio_capture, self.transcriber, self.translator]):
            print("組件未完全初始化")
            return
        
        try:
            print("開始即時翻譯處理...")
            
            # 開始音頻捕獲
            self.audio_capture.start_recording()
            
            # 開始處理線程
            self.is_processing = True
            self.processing_thread = threading.Thread(target=self._process_stream)
            self.processing_thread.daemon = True
            self.processing_thread.start()
            
            print("即時翻譯已啟動！")
            print("=" * 50)
            
        except Exception as e:
            print(f"無法開始處理: {e}")
            self.stop_processing()
    
    def stop_processing(self):
        """停止串流處理"""
        if not self.is_processing:
            return
        
        print("\n正在停止即時翻譯...")
        
        self.is_processing = False
        
        # 停止音頻捕獲
        if self.audio_capture:
            self.audio_capture.stop_recording()
        
        # 等待處理線程結束
        if self.processing_thread:
            self.processing_thread.join(timeout=3.0)
        
        print("即時翻譯已停止")
    
    def _process_stream(self):
        """處理音頻流"""
        try:
            for audio_chunk in self.audio_capture.get_audio_stream():
                if not self.is_processing:
                    break

                # 檢查音頻數據是否有效
                if len(audio_chunk) == 0:
                    continue

                # 檢查音頻音量
                volume = float(max(abs(audio_chunk.max()), abs(audio_chunk.min())))
                if volume < 0.001:  # 音量太低，跳過
                    continue

                print(f"處理音頻塊: 長度={len(audio_chunk)}, 音量={volume:.4f}")

                # 語音識別
                transcription_result = self.transcriber.transcribe_audio(audio_chunk)

                if transcription_result["text"] and transcription_result["text"].strip():
                    original_text = transcription_result["text"].strip()

                    # 檢查是否與上次輸出相同（避免重複）
                    if original_text == self.last_output_text:
                        continue

                    # 處理分段結果
                    for segment in transcription_result["segments"]:
                        start_time = segment["start"]
                        end_time = segment["end"]
                        segment_text = segment["text"].strip()

                        if segment_text and len(segment_text) > 1:  # 至少2個字符
                            # 創建分段唯一標識
                            segment_id = f"{segment_text}_{start_time:.1f}_{end_time:.1f}"

                            # 檢查是否已處理過此分段
                            if segment_id in self.processed_segments:
                                continue

                            # 記錄已處理的分段
                            self.processed_segments.add(segment_id)

                            # 執行翻譯
                            translated_text = self.translator.translate_text(segment_text)

                            # 輸出結果
                            self._output_result(segment_text, translated_text, start_time, end_time)

                            # 更新最後輸出記錄
                            self.last_output_text = segment_text
                            self.last_output_time = time.time()

                            # 調用回調函數
                            if self.on_transcription:
                                self.on_transcription(segment_text, start_time, end_time)

                            if self.on_translation:
                                self.on_translation(segment_text, translated_text, start_time, end_time)

                # 處理間隔
                time.sleep(self.processing_interval)

        except Exception as e:
            print(f"處理流錯誤: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.is_processing = False
    
    def _output_result(self, original: str, translation: str, start_time: float, end_time: float):
        """輸出翻譯結果 - 模仿字幕格式"""
        # 格式化音頻時間戳（相對於音頻開始時間）
        start_str = self._format_audio_time(start_time)
        end_str = self._format_audio_time(end_time)

        # 構建輸出內容
        output_lines = []

        # 時間戳行：00:07:14,4 --> 00:07:19,8
        if self.show_timestamps:
            output_lines.append(f"{start_str}{self.timestamp_separator}{end_str}")

        # 原文和翻譯
        if self.show_original:
            output_lines.append(f"{original}")

        if self.show_translation:
            output_lines.append(f"{translation}")

        # 分隔線
        output_lines.append(self.line_separator)

        # 輸出到控制台
        for line in output_lines:
            print(line)

        # 輸出到文件
        if self.log_to_file:
            self._log_to_file(output_lines)
    
    def _format_audio_time(self, seconds: float) -> str:
        """格式化音頻時間戳為字幕格式：00:07:14,4"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if self.show_milliseconds:
            # 顯示一位小數的秒數：00:07:14,4
            decimal_part = int((seconds % 1) * 10)  # 取一位小數
            return f"{hours:02d}:{minutes:02d}:{secs:02d},{decimal_part}"
        else:
            # 標準格式：00:07:14
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"

    def _format_time(self, seconds: float) -> str:
        """格式化時間（秒）為 HH:MM:SS,mmm 格式（保留原方法以兼容）"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)

        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _log_to_file(self, lines: list):
        """記錄到文件"""
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                for line in lines:
                    f.write(line + '\n')
        except Exception as e:
            print(f"寫入日誌文件失敗: {e}")
    
    def set_transcription_callback(self, callback: Callable[[str, float, float], None]):
        """設置轉錄回調函數"""
        self.on_transcription = callback
    
    def set_translation_callback(self, callback: Callable[[str, str, float, float], None]):
        """設置翻譯回調函數"""
        self.on_translation = callback
    
    def get_status(self) -> Dict[str, Any]:
        """獲取處理狀態"""
        return {
            "is_processing": self.is_processing,
            "audio_capture_active": self.audio_capture.is_recording if self.audio_capture else False,
            "components_initialized": all([self.audio_capture, self.transcriber, self.translator]),
            "transcriber_info": self.transcriber.get_model_info() if self.transcriber else None,
            "translator_info": self.translator.get_model_info() if self.translator else None
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_processing()
