"""
本地 LLM 翻譯模組
負責使用本地 LLM 模型進行文字翻譯
"""

import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    AutoModelForSeq2SeqLM,
    pipeline
)
from typing import List, Dict, Optional
import re
from .config_manager import ConfigManager


class LLMTranslator:
    """本地 LLM 翻譯器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化 LLM 翻譯器
        
        Args:
            config_manager: 配置管理器
        """
        self.config = config_manager.get_llm_config()
        self.translation_config = config_manager.get_translation_config()

        self.model_name = self.config.get('model_name', 'facebook/nllb-200-distilled-600M')
        self.max_length = self.config.get('max_length', 512)
        self.temperature = self.config.get('temperature', 0.3)
        self.do_sample = self.config.get('do_sample', False)

        self.system_prompt = self.translation_config.get('system_prompt', '')
        self.source_language = self.translation_config.get('source_language', '日語')
        self.target_language = self.translation_config.get('target_language', '繁體中文')

        # NLLB 語言代碼
        self.nllb_source_lang = self.translation_config.get('nllb_source_lang', 'jpn_Jpan')
        self.nllb_target_lang = self.translation_config.get('nllb_target_lang', 'zho_Hant')

        # 備用模型列表
        self.fallback_models = self.config.get('fallback_models', [
            "facebook/nllb-200-distilled-600M",
            "Helsinki-NLP/opus-mt-ja-zh",
            "simple_dict"
        ])
        
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.tokenizer: Optional[AutoTokenizer] = None
        self.model = None
        self.translator_pipeline = None
        
        print(f"使用設備: {self.device}")
        self._load_model()
    
    def _load_model(self):
        """載入翻譯模型"""
        try:
            print(f"正在載入翻譯模型: {self.model_name}")

            # 優先使用專門的翻譯模型
            if "opus-mt" in self.model_name.lower() or "translation" in self.model_name.lower() or "t5" in self.model_name.lower():
                self._load_translation_pipeline()
            elif "translation" in self.model_name.lower() or "t5" in self.model_name.lower():
                self._load_seq2seq_model()
            else:
                # 對於非翻譯模型，直接使用備用方案
                print("檢測到非翻譯模型，使用專門的翻譯模型...")
                self._load_fallback_translator()
                return

            print("翻譯模型載入完成")

        except Exception as e:
            print(f"無法載入翻譯模型: {e}")
            print("嘗試使用備用翻譯方案...")
            self._load_fallback_translator()
    
    def _load_translation_pipeline(self):
        """載入翻譯 pipeline"""
        try:
            print(f"使用翻譯 pipeline: {self.model_name}")
            self.translator_pipeline = pipeline(
                "translation",
                model=self.model_name,
                device=0 if self.device == "cuda" else -1
            )
        except Exception as e:
            print(f"翻譯 pipeline 載入失敗: {e}")
            raise

    def _load_seq2seq_model(self):
        """載入序列到序列模型"""
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        self.model = AutoModelForSeq2SeqLM.from_pretrained(
            self.model_name,
            torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
            device_map="auto" if self.device == "cuda" else None
        )

        if self.device == "cpu":
            self.model = self.model.to(self.device)
    
    def _load_causal_model(self):
        """載入因果語言模型"""
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_name,
            torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
            device_map="auto" if self.device == "cuda" else None
        )
        
        if self.device == "cpu":
            self.model = self.model.to(self.device)
        
        # 設置 pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def _load_fallback_translator(self):
        """載入備用翻譯器"""
        for model_name in self.fallback_models:
            if model_name == "simple_dict":
                print("使用簡單字典翻譯方案")
                self._setup_simple_translator()
                return

            try:
                print(f"嘗試載入翻譯模型: {model_name}")

                # 特殊處理 NLLB 模型
                if "nllb" in model_name.lower():
                    self.translator_pipeline = pipeline(
                        "translation",
                        model=model_name,
                        device=0 if self.device == "cuda" else -1,
                        src_lang=self.nllb_source_lang,
                        tgt_lang=self.nllb_target_lang
                    )
                else:
                    self.translator_pipeline = pipeline(
                        "translation",
                        model=model_name,
                        device=0 if self.device == "cuda" else -1
                    )

                # 更新模型名稱以便後續使用
                self.model_name = model_name
                print(f"✓ 成功載入翻譯模型: {model_name}")
                return

            except Exception as e:
                print(f"✗ 模型 {model_name} 載入失敗: {e}")
                continue

        print("所有翻譯模型載入失敗，將使用簡單的字典翻譯方案")
        self._setup_simple_translator()
    
    def translate_text(self, text: str) -> str:
        """
        翻譯文字
        
        Args:
            text: 要翻譯的文字
            
        Returns:
            翻譯結果
        """
        if not text or not text.strip():
            return ""
        
        text = text.strip()
        
        try:
            if self.translator_pipeline:
                return self._translate_with_pipeline(text)
            elif self.model and self.tokenizer:
                return self._translate_with_model(text)
            else:
                return self._fallback_translate(text)
                
        except Exception as e:
            print(f"翻譯錯誤: {e}")
            return f"[翻譯失敗] {text}"
    
    def _translate_with_pipeline(self, text: str) -> str:
        """使用 pipeline 進行翻譯"""
        try:
            # 檢查是否是 NLLB 模型，需要指定語言代碼
            if "nllb" in self.model_name.lower():
                result = self.translator_pipeline(
                    text,
                    src_lang=self.nllb_source_lang,
                    tgt_lang=self.nllb_target_lang,
                    max_length=self.max_length
                )
            else:
                result = self.translator_pipeline(
                    text,
                    max_length=self.max_length,
                    temperature=self.temperature,
                    do_sample=self.do_sample
                )

            if result and len(result) > 0:
                translated = result[0]['translation_text']
                # 後處理翻譯結果
                return self._post_process_translation(translated)
            return text
        except Exception as e:
            print(f"Pipeline 翻譯錯誤: {e}")
            return text

    def _post_process_translation(self, translation: str) -> str:
        """後處理翻譯結果"""
        # 移除多餘的空格和換行
        translation = translation.strip()

        # 移除可能的翻譯標記
        translation = re.sub(r'^(翻譯[:：]?\s*|中文[:：]?\s*)', '', translation)

        # 確保標點符號正確
        translation = translation.replace('。。', '。')
        translation = translation.replace('，，', '，')

        return translation
    
    def _translate_with_model(self, text: str) -> str:
        """使用載入的模型進行翻譯"""
        try:
            # 構建翻譯提示
            prompt = self._build_translation_prompt(text)
            
            # 編碼輸入
            inputs = self.tokenizer.encode(prompt, return_tensors="pt", truncation=True, max_length=self.max_length)
            inputs = inputs.to(self.device)
            
            # 生成翻譯
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 100,
                    temperature=self.temperature,
                    do_sample=self.do_sample,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    num_return_sequences=1
                )
            
            # 解碼結果
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # 提取翻譯結果
            translation = self._extract_translation(generated_text, prompt)
            
            return translation if translation else text
            
        except Exception as e:
            print(f"模型翻譯錯誤: {e}")
            return text
    
    def _build_translation_prompt(self, text: str) -> str:
        """構建翻譯提示"""
        if self.system_prompt:
            return f"{self.system_prompt}\n\n{self.source_language}: {text}\n{self.target_language}:"
        else:
            return f"請將以下{self.source_language}翻譯成{self.target_language}:\n{text}\n翻譯:"
    
    def _extract_translation(self, generated_text: str, prompt: str) -> str:
        """從生成的文字中提取翻譯結果"""
        # 移除原始提示
        if prompt in generated_text:
            translation = generated_text.replace(prompt, "").strip()
        else:
            translation = generated_text.strip()
        
        # 清理翻譯結果
        translation = re.sub(r'^翻譯[:：]?\s*', '', translation)
        translation = re.sub(r'^[繁體中文|中文][:：]?\s*', '', translation)
        
        # 取第一行作為翻譯結果
        lines = translation.split('\n')
        if lines:
            translation = lines[0].strip()
        
        return translation
    
    def _setup_simple_translator(self):
        """設置簡單的字典翻譯器"""
        self.simple_dict = {
            # 常見問候語
            "こんにちは": "你好",
            "おはよう": "早安",
            "こんばんは": "晚安",
            "さようなら": "再見",
            "はじめまして": "初次見面",

            # 感謝和道歉
            "ありがとう": "謝謝",
            "ありがとうございます": "謝謝您",
            "ありがとうございました": "謝謝您了",
            "すみません": "不好意思",
            "ごめんなさい": "對不起",

            # 工作相關
            "お疲れ様": "辛苦了",
            "お疲れ様でした": "辛苦了",
            "お疲れ様です": "辛苦了",

            # 時間相關
            "今日": "今天",
            "明日": "明天",
            "昨日": "昨天",

            # 其他常見詞彙
            "はい": "是",
            "いいえ": "不是",
            "天気": "天氣",
            "元気": "精神",
            "会いましょう": "見面吧",
            "紹介します": "介紹",
            "視聴": "觀看",
            "日本": "日本",
            "伝統的": "傳統的",
            "伝統": "傳統",
        }
        print(f"載入了 {len(self.simple_dict)} 個翻譯詞條")

    def _fallback_translate(self, text: str) -> str:
        """備用翻譯方案（簡單的字典替換）"""
        if hasattr(self, 'simple_dict'):
            # 嘗試直接匹配
            if text in self.simple_dict:
                return self.simple_dict[text]

            # 嘗試部分匹配和替換
            translated = text
            for jp_word, zh_word in self.simple_dict.items():
                if jp_word in translated:
                    translated = translated.replace(jp_word, zh_word)

            # 如果有替換發生，返回翻譯結果
            if translated != text:
                return translated

        # 如果沒有匹配，返回原文
        return text
    
    def translate_batch(self, texts: List[str]) -> List[str]:
        """
        批量翻譯
        
        Args:
            texts: 要翻譯的文字列表
            
        Returns:
            翻譯結果列表
        """
        return [self.translate_text(text) for text in texts]
    
    def get_model_info(self) -> Dict:
        """獲取模型信息"""
        return {
            "model_name": self.model_name,
            "device": self.device,
            "source_language": self.source_language,
            "target_language": self.target_language,
            "has_pipeline": self.translator_pipeline is not None,
            "has_model": self.model is not None
        }
