#!/bin/bash

# YouTube 即時翻譯工具 - Linux/macOS 版本

echo "========================================"
echo "    YouTube 即時翻譯工具"
echo "========================================"
echo
echo "此工具專門用於捕獲系統音頻並進行即時翻譯"
echo

# 檢查虛擬環境
if [ ! -f "venv/bin/activate" ]; then
    echo "❌ 虛擬環境不存在，請先運行 setup.py 或手動創建虛擬環境"
    exit 1
fi

# 激活虛擬環境
echo "正在激活虛擬環境..."
source venv/bin/activate

# 檢查依賴
echo "檢查依賴..."
python -c "import sounddevice, whisper, transformers" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少必要依賴，正在安裝..."
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ 依賴安裝失敗"
        exit 1
    fi
fi

echo
echo "========================================"
echo "步驟 1: 顯示可用音頻設備"
echo "========================================"
python main.py devices

echo
echo "========================================"
echo "步驟 2: 配置系統音頻捕獲"
echo "========================================"
echo
echo "要捕獲系統音頻，請："
echo "1. 從上面的設備列表中選擇一個設備"
echo "2. 記下設備編號"
echo

read -p "請輸入要使用的設備編號 (或按 Enter 使用預設配置): " device_choice

if [ -z "$device_choice" ]; then
    echo "使用預設的 YouTube 配置..."
    config_file="config_youtube.yaml"
else
    echo "為設備 $device_choice 創建自定義配置..."
    
    # 創建自定義配置
    cat > temp_youtube_config.yaml << EOF
# YouTube 系統音頻即時翻譯配置 - 設備 $device_choice
audio:
  sample_rate: 16000
  chunk_size: 1024
  channels: 1
  device_index: $device_choice
  input_timeout: 0.5
  capture_system_audio: true
  system_audio:
    device_index: $device_choice
    volume_threshold: 0.0005
    buffer_duration: 2.5

whisper:
  model_name: "large-v3"
  language: "ja"
  task: "transcribe"
  temperature: 0.0
  no_speech_threshold: 0.2
  condition_on_previous_text: true
  initial_prompt: "以下是日語對話、直播或影片的轉錄。"

llm:
  model_name: "facebook/nllb-200-distilled-600M"
  max_length: 256
  temperature: 0.1
  do_sample: false

translation:
  system_prompt: "你是專業的日語翻譯員，專門翻譯 YouTube 影片和直播內容。請將日文翻譯成自然流暢的繁體中文，保持原意和語氣。只提供翻譯結果。"
  source_language: "日語"
  target_language: "繁體中文"
  nllb_source_lang: "jpn_Jpan"
  nllb_target_lang: "zho_Hant"

output:
  show_timestamps: true
  timestamp_format: "%H:%M:%S,%f"
  show_original: true
  show_translation: true
  log_to_file: true
  log_file: "youtube_translation_log.txt"
  format:
    timestamp_separator: " --> "
    show_milliseconds: true
    line_separator: "----------------------------------------"

performance:
  max_audio_length: 20
  processing_interval: 0.2
  buffer_size: 2.5
  system_audio:
    processing_interval: 0.15
    min_audio_length: 0.8
    silence_threshold: 0.0005
EOF
    
    config_file="temp_youtube_config.yaml"
fi

echo
echo "========================================"
echo "步驟 3: 啟動即時翻譯"
echo "========================================"
echo
echo "配置完成！即將啟動 YouTube 即時翻譯..."
echo
echo "使用說明："
echo "- 確保音頻正在播放"
echo "- 翻譯結果將以字幕格式顯示"
echo "- 按 Ctrl+C 停止翻譯"
echo
echo "輸出格式示例："
echo "00:07:14,4 --> 00:07:19,8"
echo "見たいみんなの猫の顔流れてるやつ見たいから送ってよ"
echo "我想看大家貓咪的臉部表情，請傳給我看"
echo "----------------------------------------"
echo

read -p "按 Enter 開始翻譯..."

echo "正在啟動翻譯..."
python main.py -c "$config_file" start

echo
echo "翻譯已停止。"
if [ -f "temp_youtube_config.yaml" ]; then
    rm "temp_youtube_config.yaml"
fi
