#!/usr/bin/env python3
"""
安裝驗證腳本 - 檢查系統是否正確安裝和配置
"""

import sys
import time
from pathlib import Path

# 添加 src 目錄到 Python 路徑
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def check_dependencies():
    """檢查依賴"""
    print("檢查依賴套件...")
    
    required_packages = [
        ("sounddevice", "音頻處理"),
        ("whisper", "語音識別"),
        ("transformers", "翻譯模型"),
        ("torch", "深度學習框架"),
        ("numpy", "數值計算"),
        ("yaml", "配置文件"),
        ("click", "命令行介面")
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} - {description}")
        except ImportError:
            print(f"✗ {package} - {description} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依賴: {', '.join(missing_packages)}")
        print("請運行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依賴檢查通過")
    return True

def check_configuration():
    """檢查配置"""
    print("\n檢查配置文件...")
    
    config_files = [
        ("config.yaml", "基本配置"),
        ("config_youtube.yaml", "YouTube 專用配置")
    ]
    
    for config_file, description in config_files:
        if Path(config_file).exists():
            print(f"✓ {config_file} - {description}")
        else:
            print(f"✗ {config_file} - {description} (缺失)")
            return False
    
    print("✅ 配置文件檢查通過")
    return True

def test_basic_functionality():
    """測試基本功能"""
    print("\n測試基本功能...")
    
    try:
        from src.config_manager import ConfigManager
        from src.llm_translator import LLMTranslator
        
        # 測試配置載入
        print("- 測試配置載入...")
        config = ConfigManager("config_youtube.yaml")
        print("  ✓ 配置載入成功")
        
        # 測試翻譯器初始化
        print("- 測試翻譯器初始化...")
        translator = LLMTranslator(config)
        print("  ✓ 翻譯器初始化成功")
        
        # 簡單翻譯測試
        print("- 測試翻譯功能...")
        test_text = "こんにちは"
        translation = translator.translate_text(test_text)
        print(f"  原文: {test_text}")
        print(f"  翻譯: {translation}")
        print("  ✓ 翻譯功能正常")
        
        print("✅ 基本功能測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 功能測試失敗: {e}")
        return False

def show_usage_instructions():
    """顯示使用說明"""
    print("\n" + "=" * 60)
    print("🎉 安裝驗證完成！")
    print("=" * 60)
    
    print("\n📋 使用方法:")
    print("1. YouTube 即時翻譯 (推薦):")
    print("   Windows: 雙擊 start_youtube.bat")
    print("   Linux/macOS: ./start_youtube.sh")
    print()
    print("2. 手動啟動:")
    print("   python main.py -c config_youtube.yaml start")
    print()
    print("3. 查看音頻設備:")
    print("   python main.py devices")
    
    print("\n💡 提示:")
    print("- 確保 YouTube 或其他音頻正在播放")
    print("- 選擇支援系統音頻捕獲的輸出設備")
    print("- 翻譯結果會以字幕格式顯示")
    
    print("\n🎯 輸出格式:")
    print("00:07:14,4 --> 00:07:19,8")
    print("こんにちは、元気ですか？")
    print("你好，你好嗎？")
    print("----------------------------------------")

def main():
    """主函數"""
    print("=" * 60)
    print("本地即時翻譯工具 - 安裝驗證")
    print("=" * 60)
    
    # 檢查依賴
    if not check_dependencies():
        return False
    
    # 檢查配置
    if not check_configuration():
        return False
    
    # 測試功能
    if not test_basic_functionality():
        return False
    
    # 顯示使用說明
    show_usage_instructions()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 驗證失敗，請檢查上述錯誤並重新安裝")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n驗證已取消")
    except Exception as e:
        print(f"\n❌ 驗證過程中發生錯誤: {e}")
        sys.exit(1)
