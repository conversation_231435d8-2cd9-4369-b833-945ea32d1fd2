# 本地即時翻譯工具 - 快速開始指南

## 🎉 恭喜！您的翻譯工具已經準備就緒

### ✅ 已解決的問題

1. **依賴安裝問題** - 已修復 PyAudio 編譯問題，改用 sounddevice
2. **翻譯功能問題** - 已成功整合 Facebook NLLB 翻譯模型
3. **編碼問題** - 已修復中文編碼問題
4. **啟動腳本問題** - 已創建可用的啟動腳本

### 🚀 立即開始使用

#### 方法一：使用英文啟動腳本（推薦）

```bash
# 雙擊運行或在命令行中執行
start.bat
```

#### 方法二：直接使用 Python

```bash
# 激活虛擬環境並運行
venv\Scripts\activate
python main.py start
```

#### 方法三：使用 PowerShell 腳本

```powershell
# 在 PowerShell 中執行
powershell -ExecutionPolicy Bypass -File run.ps1
```

### 📊 系統狀態

當您啟動工具時，您會看到：

```
╭──────────────────────────────────────────╮
│ 本地即時翻譯工具                         │
│ 使用 Whisper + 本地 LLM 進行即時語音翻譯 │
╰──────────────────────────────────────────╯

✓ 配置文件載入成功
✓ 音頻捕獲器初始化完成
✓ 語音識別器初始化完成 (Whisper large-v3)
✓ 翻譯器初始化完成 (NLLB 模型)

系統狀態:
┏━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┓
┃ 組件     ┃ 狀態     ┃ 詳細信息        ┃
┡━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━┩
│ 處理器   │ ✓ 運行中 │ 即時翻譯處理    │
│ 音頻捕獲 │ ✓ 活躍   │ 麥克風音頻輸入  │
│ 語音識別 │ ✓ 就緒   │ large-v3 (ja)   │
│ 翻譯器   │ ✓ 就緒   │ 日語 → 繁體中文 │
└──────────┴──────────┴─────────────────┘

按 Ctrl+C 停止翻譯
```

### 📝 翻譯輸出格式

當系統檢測到日語語音時，會顯示：

```
[14:30:25] 00:00:01,500 --> 00:00:03,200
原文: こんにちは、元気ですか？
翻譯: 你好，你好嗎？
----------------------------------------
```

- `[14:30:25]` - 當前時間戳
- `00:00:01,500 --> 00:00:03,200` - 音頻中的時間範圍
- `原文` - Whisper 識別的日語文字
- `翻譯` - NLLB 模型翻譯的中文結果

### 🔧 可用命令

```bash
# 開始即時翻譯
python main.py start

# 列出音頻設備
python main.py devices

# 測試系統組件
python main.py test

# 顯示配置信息
python main.py config-info

# 顯示幫助
python main.py --help
```

### 🧪 測試功能

```bash
# 測試翻譯功能
python test_translation.py

# 測試立體聲混音 (YouTube 音頻捕獲)
python test_stereo_mix.py

# 運行安裝檢查
python install_check.py

# 查看功能演示
python demo.py
```

### ⚙️ 配置調整

編輯 `config.yaml` 文件來自定義設置：

```yaml
# 音頻設置
audio:
  sample_rate: 16000      # 採樣率
  device_index: null      # 音頻設備（null=默認）

# Whisper 設置
whisper:
  model_name: "large-v3"  # 模型大小
  language: "ja"          # 識別語言

# 翻譯設置
llm:
  model_name: "facebook/nllb-200-distilled-600M"
```

### 🎯 使用技巧

1. **音頻設備選擇**
   - 運行 `python main.py devices` 查看可用設備
   - 在 `config.yaml` 中設置 `device_index`

2. **YouTube 音頻捕獲** ⭐
   - 啟用 Windows 立體聲混音功能
   - 運行 `python test_stereo_mix.py` 測試設置
   - 詳細指南請參考 `YOUTUBE_SETUP.md`

3. **提高翻譯品質**
   - 確保音頻清晰
   - 避免背景噪音
   - 說話速度適中

4. **性能優化**
   - 首次運行會下載模型，需要時間
   - 後續運行會使用緩存的模型
   - 使用 GPU 可以提升速度

### 🐛 故障排除

#### 常見問題

1. **音頻設備問題**

   ```bash
   python main.py devices  # 查看可用設備
   ```

2. **模型下載失敗**
   - 檢查網路連接
   - 重新運行程式

3. **翻譯品質問題**
   - 調整音頻設備
   - 確保語音清晰

#### 錯誤信息

- `FP16 is not supported on CPU` - 正常警告，不影響功能
- `Translation requires src_lang and tgt_lang` - 已修復
- `PyAudio compilation error` - 已改用 sounddevice

### 📁 文件結構

```
local-translate/
├── start.bat              # 英文啟動腳本（推薦）
├── run.ps1                # PowerShell 啟動腳本
├── main.py                # 主程式
├── config.yaml            # 配置文件
├── test_translation.py    # 翻譯測試
├── install_check.py       # 安裝檢查
├── demo.py                # 功能演示
└── src/                   # 源代碼目錄
```

### 🎊 享受您的翻譯工具

您的本地即時翻譯工具現在已經完全可用：

- ✅ 支援日語語音識別
- ✅ 支援即時中文翻譯
- ✅ 完全本地化處理
- ✅ 一鍵啟動
- ✅ 豐富的配置選項

**開始使用：雙擊 `start.bat` 或運行 `python main.py start`**

如有問題，請查看 `USAGE_GUIDE.md` 獲取詳細說明。
