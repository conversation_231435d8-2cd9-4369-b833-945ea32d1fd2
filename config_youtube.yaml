# YouTube 系統音頻即時翻譯配置文件
# 專門用於捕獲 YouTube 直播和影片音頻進行即時翻譯

# 音頻設置 - 針對系統音頻優化
audio:
  sample_rate: 16000
  chunk_size: 1024
  channels: 1
  device_index: 0  # Microsoft 音效對應表，適合系統音頻捕獲
  input_timeout: 0.5  # 較短的超時時間以提高響應速度
  capture_system_audio: true  # 啟用系統音頻捕獲
  
  # 系統音頻專用設置
  system_audio:
    device_index: 0  # Microsoft 音效對應表
    volume_threshold: 0.0005  # 非常低的音量閾值以捕獲所有音頻
    buffer_duration: 2.5  # 音頻緩衝時長（秒）
    use_wasapi_loopback: true  # 使用 WASAPI loopback 模式

# Whisper 設置 - 針對日語優化
whisper:
  model_name: "large-v3"  # 使用最大模型以獲得最佳識別效果
  language: "ja"  # 日語
  task: "transcribe"
  temperature: 0.0  # 確定性轉錄
  no_speech_threshold: 0.2  # 降低無語音閾值以捕獲更多內容
  condition_on_previous_text: true
  initial_prompt: "以下是日語對話、直播或影片的轉錄，包含日常對話、解說和評論。"

# LLM 翻譯設置 - 針對即時翻譯優化
llm:
  model_name: "facebook/nllb-200-distilled-600M"
  max_length: 256  # 較短的最大長度以提高速度
  temperature: 0.1  # 非常低的溫度以獲得一致的翻譯
  do_sample: false
  pad_token_id: 50256

# 翻譯提示詞 - 針對 YouTube 內容優化
translation:
  system_prompt: "你是專業的日語翻譯員，專門翻譯 YouTube 影片和直播內容。請將日文翻譯成自然流暢的繁體中文，保持原意和語氣。對於網路用語、流行語和專有名詞請適當本地化。只提供翻譯結果，不要添加解釋。"
  source_language: "日語"
  target_language: "繁體中文"
  nllb_source_lang: "jpn_Jpan"
  nllb_target_lang: "zho_Hant"

  # 翻譯品質設置
  quality_settings:
    use_context: true  # 使用上下文提高翻譯品質
    preserve_tone: true  # 保持語氣
    localize_terms: true  # 本地化專有名詞

# 輸出設置 - 模仿字幕格式
output:
  show_timestamps: true
  timestamp_format: "%H:%M:%S,%f"  # 包含毫秒：00:07:14,4
  show_original: true
  show_translation: true
  log_to_file: true
  log_file: "youtube_translation_log.txt"
  
  format:
    timestamp_separator: " --> "
    show_milliseconds: true
    line_separator: "----------------------------------------"
    # 輸出格式：
    # 00:07:14,4 --> 00:07:19,8
    # 原文: 見たいみんなの猫の顔流れてるやつ見たいから送ってよ
    # 翻譯: 我想看大家貓咪的臉部表情，請傳給我看

# 性能設置 - 針對即時性優化
performance:
  max_audio_length: 20  # 較短的最大音頻長度
  processing_interval: 0.2  # 非常短的處理間隔以提高即時性
  buffer_size: 2.5  # 較小的緩衝區
  
  system_audio:
    processing_interval: 0.15  # 極短的處理間隔
    min_audio_length: 0.8  # 最小音頻長度
    silence_threshold: 0.0005  # 極低的靜音閾值

# YouTube 專用設置
youtube:
  # 常見的 YouTube 音頻設備
  recommended_devices:
    - 0  # Microsoft 音效對應表 - Input
    - 3  # Microsoft 音效對應表 - Output
  
  # 音頻品質設置
  audio_quality:
    prefer_stereo: false  # 使用單聲道以節省資源
    noise_reduction: true  # 啟用降噪
    auto_gain_control: true  # 自動增益控制
