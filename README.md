# 本地即時翻譯工具

一個使用 Whisper large-v3 和本地 LLM 的即時語音翻譯工具，支援日語到繁體中文的即時翻譯。

## 功能特色

- 🎤 **即時音頻捕獲**: 支援麥克風或系統音頻輸入
- 🗣️ **高精度語音識別**: 使用 OpenAI Whisper large-v3 模型
- 🌐 **本地 LLM 翻譯**: 完全本地化的翻譯處理，保護隱私
- ⏱️ **時間戳支援**: 精確的時間戳記錄
- 🎯 **專業翻譯**: 針對日語到繁體中文優化
- 💻 **命令行介面**: 簡潔易用的 CLI 介面
- 🚀 **一鍵啟動**: 提供腳本化執行，無需開啟 IDE

## 系統需求

- Python 3.8 或更高版本
- 至少 8GB RAM（推薦 16GB）
- 支援 CUDA 的 GPU（可選，用於加速）
- 麥克風或音頻輸入設備

## 快速開始

### 1. 使用啟動腳本（推薦）

**Windows 用戶:**

```bash
# 雙擊運行或在命令行中執行
run.bat
```

**Linux/macOS 用戶:**

```bash
# 給腳本執行權限
chmod +x run.sh
# 運行腳本
./run.sh
```

### 2. 手動安裝和運行

```bash
# 1. 創建虛擬環境
python -m venv venv

# 2. 激活虛擬環境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 3. 安裝依賴
pip install -r requirements.txt

# 4. 運行程式
python main.py start
```

## 使用方法

### 基本命令

```bash
# 開始即時翻譯
python main.py start

# 列出可用的音頻設備
python main.py devices

# 測試系統組件
python main.py test

# 顯示配置信息
python main.py config-info

# 顯示幫助
python main.py --help
```

### 配置文件

編輯 `config.yaml` 文件來自定義設置：

```yaml
# 音頻設置
audio:
  sample_rate: 16000
  device_index: null  # 使用默認設備

# Whisper 設置
whisper:
  model_name: "large-v3"
  language: "ja"

# 翻譯設置
translation:
  source_language: "日語"
  target_language: "繁體中文"
```

## 輸出格式

程式會即時輸出翻譯結果，格式如下：

```
[14:30:25] 00:00:01,500 --> 00:00:03,200
原文: こんにちは、元気ですか？
翻譯: 你好，你好嗎？
----------------------------------------
```

## 故障排除

### 常見問題

1. **音頻設備問題**

   ```bash
   # 列出可用設備
   python main.py devices
   # 在 config.yaml 中設置正確的 device_index
   ```

2. **模型載入失敗**
   - 確保有足夠的 RAM 和存儲空間
   - 檢查網路連接（首次下載模型時需要）

3. **翻譯品質問題**
   - 調整 `config.yaml` 中的翻譯提示詞
   - 嘗試不同的 LLM 模型

### 性能優化

- 使用 CUDA GPU 可顯著提升處理速度
- 調整 `config.yaml` 中的 `processing_interval` 來平衡延遲和性能
- 對於低配置設備，可以使用較小的 Whisper 模型（如 `base` 或 `small`）

## 開發

### 專案結構

```
local-translate/
├── src/                    # 源代碼
│   ├── audio_capture.py   # 音頻捕獲模組
│   ├── whisper_transcriber.py  # Whisper 語音識別
│   ├── llm_translator.py  # LLM 翻譯模組
│   ├── stream_processor.py  # 串流處理器
│   ├── config_manager.py  # 配置管理
│   └── cli.py            # 命令行介面
├── config.yaml           # 配置文件
├── requirements.txt      # 依賴列表
├── main.py              # 主程式入口
├── run.bat              # Windows 啟動腳本
├── run.sh               # Linux/macOS 啟動腳本
└── README.md            # 說明文件
```

### 自定義開發

您可以通過以下方式擴展功能：

1. **添加新的翻譯模型**: 修改 `llm_translator.py`
2. **支援更多語言**: 更新 `config.yaml` 和相關模組
3. **添加新的輸出格式**: 修改 `stream_processor.py`

## 授權

MIT License

## 貢獻

歡迎提交 Issue 和 Pull Request！

## 更新日誌

### v1.0.0

- 初始版本發布
- 支援日語到繁體中文即時翻譯
- 完整的 CLI 介面
- 腳本化執行支援
