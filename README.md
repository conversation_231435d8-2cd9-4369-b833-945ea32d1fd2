# 本地即時翻譯工具

一個專門用於 **YouTube 直播和影片** 的即時語音翻譯工具，使用 Whisper large-v3 和本地 LLM 進行高品質的日語到繁體中文翻譯。

## 功能特色

- 🎥 **YouTube 專用**: 專門優化用於捕獲 YouTube 直播和影片音頻
- 🎤 **系統音頻捕獲**: 支援 Windows WASAPI loopback 技術捕獲系統音頻
- 🗣️ **高精度語音識別**: 使用 OpenAI Whisper large-v3 模型，專門針對日語優化
- 🌐 **本地 LLM 翻譯**: 使用 Facebook NLLB 模型，完全本地化處理，保護隱私
- ⏱️ **字幕格式輸出**: 精確的時間戳格式 (00:07:14,4 --> 00:07:19,8)
- 🎯 **專業翻譯**: 針對 YouTube 內容和日常對話優化
- 💻 **一鍵啟動**: 專用的 YouTube 啟動腳本，自動配置最佳設置
- 🚀 **即時處理**: 低延遲的即時翻譯，適合直播觀看

## 系統需求

- Python 3.8 或更高版本
- 至少 8GB RAM（推薦 16GB）
- 支援 CUDA 的 GPU（可選，用於加速）
- 麥克風或音頻輸入設備

## 快速開始

### 🚀 YouTube 即時翻譯（推薦）

**Windows 用戶:**

```bash
# 雙擊運行 YouTube 專用啟動腳本
start_youtube.bat
```

**Linux/macOS 用戶:**

```bash
# 運行 YouTube 專用啟動腳本
./start_youtube.sh
```

### 📋 使用步驟

1. **運行啟動腳本** - 腳本會自動顯示可用的音頻設備
2. **選擇輸出設備** - 選擇支援系統音頻捕獲的設備（通常是揚聲器設備）
3. **開始翻譯** - 打開 YouTube 影片或直播，開始享受即時翻譯！

### 🎯 輸出效果

```text
00:07:14,4 --> 00:07:19,8
見たいみんなの猫の顔流れてるやつ見たいから送ってよ
我想看大家貓咪的臉部表情，請傳給我看
----------------------------------------
```

### 🔧 手動安裝和運行

```bash
# 1. 創建虛擬環境
python -m venv venv

# 2. 激活虛擬環境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 3. 安裝依賴
pip install -r requirements.txt

# 4. 驗證安裝
python verify_installation.py

# 5. 運行 YouTube 翻譯
python main.py -c config_youtube.yaml start
```

## 🎮 使用方法

### 📱 基本命令

```bash
# YouTube 即時翻譯 (推薦)
python main.py -c config_youtube.yaml start

# 列出可用的音頻設備
python main.py devices

# 驗證安裝
python verify_installation.py

# 顯示幫助
python main.py --help
```

### ⚙️ 系統音頻設置

1. **查看可用設備**: 運行 `python main.py devices`
2. **選擇輸出設備**: 選擇標有 "(支援系統音頻捕獲)" 的設備
3. **配置設備**: 在啟動腳本中輸入設備編號，或手動編輯配置文件

### 🎯 配置文件

主要配置文件：

- `config_youtube.yaml` - YouTube 專用配置（推薦）
- `config.yaml` - 基本配置

YouTube 配置示例：

```yaml
audio:
  device_index: 0  # Microsoft 音效對應表
  capture_system_audio: true

whisper:
  model_name: "large-v3"
  language: "ja"

translation:
  nllb_source_lang: "jpn_Jpan"
  nllb_target_lang: "zho_Hant"
```

## 故障排除

### 常見問題

1. **音頻設備問題**

   ```bash
   # 列出可用設備
   python main.py devices
   # 在 config.yaml 中設置正確的 device_index
   ```

2. **模型載入失敗**
   - 確保有足夠的 RAM 和存儲空間
   - 檢查網路連接（首次下載模型時需要）

3. **翻譯品質問題**
   - 調整 `config.yaml` 中的翻譯提示詞
   - 嘗試不同的 LLM 模型

### 性能優化

- 使用 CUDA GPU 可顯著提升處理速度
- 調整 `config.yaml` 中的 `processing_interval` 來平衡延遲和性能
- 對於低配置設備，可以使用較小的 Whisper 模型（如 `base` 或 `small`）

## 開發

### 專案結構

```
local-translate/
├── src/                    # 源代碼
│   ├── audio_capture.py   # 音頻捕獲模組
│   ├── whisper_transcriber.py  # Whisper 語音識別
│   ├── llm_translator.py  # LLM 翻譯模組
│   ├── stream_processor.py  # 串流處理器
│   ├── config_manager.py  # 配置管理
│   └── cli.py            # 命令行介面
├── config.yaml           # 配置文件
├── requirements.txt      # 依賴列表
├── main.py              # 主程式入口
├── run.bat              # Windows 啟動腳本
├── run.sh               # Linux/macOS 啟動腳本
└── README.md            # 說明文件
```

### 自定義開發

您可以通過以下方式擴展功能：

1. **添加新的翻譯模型**: 修改 `llm_translator.py`
2. **支援更多語言**: 更新 `config.yaml` 和相關模組
3. **添加新的輸出格式**: 修改 `stream_processor.py`

## 授權

MIT License

## 貢獻

歡迎提交 Issue 和 Pull Request！

## 更新日誌

### v1.0.0

- 初始版本發布
- 支援日語到繁體中文即時翻譯
- 完整的 CLI 介面
- 腳本化執行支援
