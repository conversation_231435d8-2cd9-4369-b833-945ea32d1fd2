@echo off
chcp 65001 >nul
title YouTube 即時翻譯工具

echo ========================================
echo    YouTube 即時翻譯工具
echo ========================================
echo.
echo 此工具專門用於捕獲 YouTube 等系統音頻並進行即時翻譯
echo.

REM 檢查虛擬環境
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 虛擬環境不存在，請先運行 setup.py 或手動創建虛擬環境
    pause
    exit /b 1
)

REM 激活虛擬環境
echo 正在激活虛擬環境...
call venv\Scripts\activate.bat

REM 檢查依賴
echo 檢查依賴...
python -c "import sounddevice, whisper, transformers" 2>nul
if errorlevel 1 (
    echo ❌ 缺少必要依賴，正在安裝...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依賴安裝失敗
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo 步驟 1: 顯示可用音頻設備
echo ========================================
python main.py devices

echo.
echo ========================================
echo 步驟 2: 配置系統音頻捕獲
echo ========================================
echo.
echo 要捕獲 YouTube 等系統音頻，請：
echo 1. 從上面的輸出設備列表中選擇一個設備
echo 2. 建議選擇標有 "(支援系統音頻捕獲)" 的設備
echo 3. 記下設備編號
echo.

set /p device_choice="請輸入要使用的輸出設備編號 (或按 Enter 使用預設配置): "

if "%device_choice%"=="" (
    echo 使用預設的 YouTube 配置...
    set config_file=config_youtube.yaml
) else (
    echo 為設備 %device_choice% 創建自定義配置...
    
    REM 創建自定義配置
    (
        echo # YouTube 系統音頻即時翻譯配置 - 設備 %device_choice%
        echo audio:
        echo   sample_rate: 16000
        echo   chunk_size: 1024
        echo   channels: 1
        echo   device_index: %device_choice%
        echo   input_timeout: 0.5
        echo   capture_system_audio: true
        echo   system_audio:
        echo     device_index: %device_choice%
        echo     volume_threshold: 0.0005
        echo     buffer_duration: 2.5
        echo.
        echo whisper:
        echo   model_name: "large-v3"
        echo   language: "ja"
        echo   task: "transcribe"
        echo   temperature: 0.0
        echo   no_speech_threshold: 0.2
        echo   condition_on_previous_text: true
        echo   initial_prompt: "以下是日語對話、直播或影片的轉錄。"
        echo.
        echo llm:
        echo   model_name: "facebook/nllb-200-distilled-600M"
        echo   max_length: 256
        echo   temperature: 0.1
        echo   do_sample: false
        echo.
        echo translation:
        echo   system_prompt: "你是專業的日語翻譯員，專門翻譯 YouTube 影片和直播內容。請將日文翻譯成自然流暢的繁體中文，保持原意和語氣。只提供翻譯結果。"
        echo   source_language: "日語"
        echo   target_language: "繁體中文"
        echo   nllb_source_lang: "jpn_Jpan"
        echo   nllb_target_lang: "zho_Hant"
        echo.
        echo output:
        echo   show_timestamps: true
        echo   timestamp_format: "%%H:%%M:%%S,%%f"
        echo   show_original: true
        echo   show_translation: true
        echo   log_to_file: true
        echo   log_file: "youtube_translation_log.txt"
        echo   format:
        echo     timestamp_separator: " --> "
        echo     show_milliseconds: true
        echo     line_separator: "----------------------------------------"
        echo.
        echo performance:
        echo   max_audio_length: 20
        echo   processing_interval: 0.2
        echo   buffer_size: 2.5
        echo   system_audio:
        echo     processing_interval: 0.15
        echo     min_audio_length: 0.8
        echo     silence_threshold: 0.0005
    ) > temp_youtube_config.yaml
    
    set config_file=temp_youtube_config.yaml
)

echo.
echo ========================================
echo 步驟 3: 啟動即時翻譯
echo ========================================
echo.
echo 配置完成！即將啟動 YouTube 即時翻譯...
echo.
echo 使用說明：
echo - 確保 YouTube 或其他音頻正在播放
echo - 翻譯結果將以字幕格式顯示
echo - 按 Ctrl+C 停止翻譯
echo.
echo 輸出格式示例：
echo 00:07:14,4 --> 00:07:19,8
echo 見たいみんなの猫の顔流れてるやつ見たいから送ってよ
echo 我想看大家貓咪的臉部表情，請傳給我看
echo ----------------------------------------
echo.

pause

echo 正在啟動翻譯...
python main.py -c %config_file% start

echo.
echo 翻譯已停止。
if exist "temp_youtube_config.yaml" del "temp_youtube_config.yaml"
pause
