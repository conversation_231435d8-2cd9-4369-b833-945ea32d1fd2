#!/usr/bin/env python3
"""
翻譯功能測試腳本
"""

import sys
from pathlib import Path

# 添加 src 目錄到 Python 路徑
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_translation():
    """測試翻譯功能"""
    print("=" * 50)
    print("翻譯功能測試")
    print("=" * 50)
    
    try:
        from src.config_manager import ConfigManager
        from src.llm_translator import LLMTranslator
        
        # 載入配置
        print("載入配置...")
        config = ConfigManager("config.yaml")
        
        # 初始化翻譯器
        print("初始化翻譯器...")
        translator = LLMTranslator(config)
        
        # 測試文本
        test_texts = [
            "こんにちは",
            "今日はいい天気ですね",
            "ありがとうございました",
            "お疲れ様でした",
            "明日また会いましょう",
            "次は、日本の伝統的な伝統的な伝統的な伝統を紹介します。",
            "ご視聴ありがとうございました。"
        ]
        
        print("\n開始翻譯測試...")
        print("-" * 50)
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n測試 {i}:")
            print(f"原文: {text}")
            
            try:
                translation = translator.translate_text(text)
                print(f"翻譯: {translation}")
                
                # 檢查翻譯是否成功
                if translation == text:
                    print("⚠️  翻譯結果與原文相同，可能翻譯失敗")
                elif "[翻譯失敗]" in translation or "[需要翻譯]" in translation:
                    print("❌ 翻譯失敗")
                else:
                    print("✅ 翻譯成功")
                    
            except Exception as e:
                print(f"❌ 翻譯錯誤: {e}")
        
        print("\n" + "-" * 50)
        print("翻譯測試完成")
        
        # 顯示模型信息
        model_info = translator.get_model_info()
        print("\n模型信息:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_translation()
