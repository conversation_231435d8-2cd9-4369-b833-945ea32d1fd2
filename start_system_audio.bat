@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Local Translation Tool - System Audio
echo ========================================
echo This script captures system audio (YouTube, etc.)
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo ERROR: Virtual environment not found
    echo Please run start.bat first to set up the environment
    pause
    exit /b 1
)

REM Activate virtual environment
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Cannot activate virtual environment
    pause
    exit /b 1
)

echo Step 1: List available audio devices
echo =====================================
python main.py devices

echo.
echo Step 2: Configure system audio capture
echo ======================================
echo To capture system audio (like YouTube), you need to:
echo 1. Find your speaker/headphone device number from the list above
echo 2. Look for devices marked with "(supports system audio capture)"
echo 3. Note the device number
echo.

set /p device_choice="Enter the OUTPUT device number for system audio capture (or press Enter to use default): "

if not "%device_choice%"=="" (
    echo Updating configuration for device %device_choice%...
    
    REM Create temporary config with system audio settings
    (
        echo # System audio configuration
        echo audio:
        echo   sample_rate: 16000
        echo   chunk_size: 1024
        echo   channels: 1
        echo   device_index: %device_choice%
        echo   input_timeout: 1.0
        echo   capture_system_audio: true
        echo.
        echo whisper:
        echo   model_name: "large-v3"
        echo   language: "ja"
        echo   task: "transcribe"
        echo   temperature: 0.0
        echo   no_speech_threshold: 0.1
        echo   condition_on_previous_text: true
        echo   initial_prompt: "以下是日語對話的轉錄。"
        echo.
        echo llm:
        echo   model_name: "facebook/nllb-200-distilled-600M"
        echo   max_length: 512
        echo   temperature: 0.7
        echo   do_sample: true
        echo   pad_token_id: 50256
        echo.
        echo translation:
        echo   system_prompt: "你是一位專業翻譯員。請將日文字幕翻譯成正體中文。並且盡可能貼近一些日常的用語，請勿做任何額外解釋、註釋，直接翻譯出來就好。"
        echo   source_language: "日語"
        echo   target_language: "繁體中文"
        echo.
        echo output:
        echo   show_timestamps: true
        echo   timestamp_format: "%%H:%%M:%%S"
        echo   show_original: true
        echo   show_translation: true
        echo   log_to_file: false
        echo   log_file: "translation_log.txt"
        echo.
        echo performance:
        echo   max_audio_length: 30
        echo   processing_interval: 0.3
        echo   buffer_size: 3
    ) > temp_config.yaml
    
    echo Configuration updated for device %device_choice%
) else (
    echo Using default system audio configuration...
    copy config_system_audio.yaml temp_config.yaml >nul
)

echo.
echo Step 3: Start system audio translation
echo ======================================
echo IMPORTANT: 
echo 1. Make sure your YouTube video or audio is playing
echo 2. The volume should be audible (not muted)
echo 3. Press Ctrl+C to stop translation
echo.
echo Starting translation tool...
echo.

python main.py -c temp_config.yaml start

echo.
echo ========================================
echo Translation stopped
echo ========================================

REM Clean up temporary config
if exist temp_config.yaml del temp_config.yaml

pause
