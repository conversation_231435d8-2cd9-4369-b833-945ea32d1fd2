# This file is autogenerated by the command `make fix-copies`, do not edit.
from ..utils import DummyObject, requires_backends


class TensorFlowBenchmarkArguments(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TensorFlowBenchmark(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFForcedBOSTokenLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFForcedEOSTokenLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFForceTokensLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGenerationMixin(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLogitsProcessorList(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLogitsWarper(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMinLengthLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFNoBadWordsLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFNoRepeatNGramLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRepetitionPenaltyLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSuppressTokensAtBeginLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSuppressTokensLogitsProcessor(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTemperatureLogitsWarper(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTopKLogitsWarper(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTopPLogitsWarper(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


def tf_top_k_top_p_filtering(*args, **kwargs):
    requires_backends(tf_top_k_top_p_filtering, ["tf"])


class KerasMetricCallback(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class PushToHubCallback(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSequenceSummary(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSharedEmbeddings(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


def shape_list(*args, **kwargs):
    requires_backends(shape_list, ["tf"])


TF_ALBERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFAlbertForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAlbertForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAlbertForPreTraining(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAlbertForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAlbertForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAlbertForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAlbertMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAlbertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAlbertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_MODEL_FOR_AUDIO_CLASSIFICATION_MAPPING = None


TF_MODEL_FOR_CAUSAL_LM_MAPPING = None


TF_MODEL_FOR_DOCUMENT_QUESTION_ANSWERING_MAPPING = None


TF_MODEL_FOR_IMAGE_CLASSIFICATION_MAPPING = None


TF_MODEL_FOR_MASK_GENERATION_MAPPING = None


TF_MODEL_FOR_MASKED_IMAGE_MODELING_MAPPING = None


TF_MODEL_FOR_MASKED_LM_MAPPING = None


TF_MODEL_FOR_MULTIPLE_CHOICE_MAPPING = None


TF_MODEL_FOR_NEXT_SENTENCE_PREDICTION_MAPPING = None


TF_MODEL_FOR_PRETRAINING_MAPPING = None


TF_MODEL_FOR_QUESTION_ANSWERING_MAPPING = None


TF_MODEL_FOR_SEMANTIC_SEGMENTATION_MAPPING = None


TF_MODEL_FOR_SEQ_TO_SEQ_CAUSAL_LM_MAPPING = None


TF_MODEL_FOR_SEQUENCE_CLASSIFICATION_MAPPING = None


TF_MODEL_FOR_SPEECH_SEQ_2_SEQ_MAPPING = None


TF_MODEL_FOR_TABLE_QUESTION_ANSWERING_MAPPING = None


TF_MODEL_FOR_TEXT_ENCODING_MAPPING = None


TF_MODEL_FOR_TOKEN_CLASSIFICATION_MAPPING = None


TF_MODEL_FOR_VISION_2_SEQ_MAPPING = None


TF_MODEL_FOR_ZERO_SHOT_IMAGE_CLASSIFICATION_MAPPING = None


TF_MODEL_MAPPING = None


TF_MODEL_WITH_LM_HEAD_MAPPING = None


class TFAutoModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForAudioClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForDocumentQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForMaskedImageModeling(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForMaskGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForNextSentencePrediction(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForPreTraining(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForSemanticSegmentation(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForSeq2SeqLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForSpeechSeq2Seq(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForTableQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForTextEncoding(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForVision2Seq(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelForZeroShotImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFAutoModelWithLMHead(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBartForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBartForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBartModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBartPretrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_BERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFBertEmbeddings(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertForNextSentencePrediction(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertForPreTraining(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertLMHeadModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlenderbotForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlenderbotModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlenderbotPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlenderbotSmallForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlenderbotSmallModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlenderbotSmallPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_BLIP_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFBlipForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlipForImageTextRetrieval(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlipForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlipModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlipPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlipTextModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFBlipVisionModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_CAMEMBERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFCamembertForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCamembertForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCamembertForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCamembertForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCamembertForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCamembertForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCamembertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCamembertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_CLIP_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFCLIPModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCLIPPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCLIPTextModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCLIPVisionModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_CONVBERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFConvBertForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvBertForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvBertForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvBertForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvBertForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvBertLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvBertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvBertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvNextForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvNextModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvNextPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvNextV2ForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvNextV2Model(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFConvNextV2PreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_CTRL_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFCTRLForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCTRLLMHeadModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCTRLModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCTRLPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_CVT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFCvtForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCvtModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFCvtPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFData2VecVisionForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFData2VecVisionForSemanticSegmentation(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFData2VecVisionModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFData2VecVisionPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_DEBERTA_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFDebertaForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_DEBERTA_V2_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFDebertaV2ForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaV2ForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaV2ForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaV2ForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaV2ForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaV2Model(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDebertaV2PreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_DEIT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFDeiTForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDeiTForImageClassificationWithTeacher(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDeiTForMaskedImageModeling(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDeiTModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDeiTPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_DISTILBERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFDistilBertForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDistilBertForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDistilBertForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDistilBertForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDistilBertForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDistilBertMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDistilBertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDistilBertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_DPR_CONTEXT_ENCODER_PRETRAINED_MODEL_ARCHIVE_LIST = None


TF_DPR_QUESTION_ENCODER_PRETRAINED_MODEL_ARCHIVE_LIST = None


TF_DPR_READER_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFDPRContextEncoder(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDPRPretrainedContextEncoder(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDPRPretrainedQuestionEncoder(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDPRPretrainedReader(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDPRQuestionEncoder(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFDPRReader(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_EFFICIENTFORMER_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFEfficientFormerForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFEfficientFormerForImageClassificationWithTeacher(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFEfficientFormerModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFEfficientFormerPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_ELECTRA_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFElectraForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFElectraForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFElectraForPreTraining(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFElectraForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFElectraForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFElectraForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFElectraModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFElectraPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFEncoderDecoderModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


ESM_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFEsmForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFEsmForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFEsmForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFEsmModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFEsmPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_FLAUBERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFFlaubertForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFlaubertForQuestionAnsweringSimple(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFlaubertForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFlaubertForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFlaubertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFlaubertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFlaubertWithLMHeadModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_FUNNEL_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFFunnelBaseModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFunnelForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFunnelForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFunnelForPreTraining(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFunnelForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFunnelForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFunnelForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFunnelModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFFunnelPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_GPT2_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFGPT2DoubleHeadsModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPT2ForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPT2LMHeadModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPT2MainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPT2Model(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPT2PreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPTJForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPTJForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPTJForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPTJModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGPTJPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_GROUPVIT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFGroupViTModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGroupViTPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGroupViTTextModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFGroupViTVisionModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_HUBERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFHubertForCTC(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFHubertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFHubertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_LAYOUTLM_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFLayoutLMForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_LAYOUTLMV3_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFLayoutLMv3ForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMv3ForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMv3ForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMv3Model(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLayoutLMv3PreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLEDForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLEDModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLEDPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_LONGFORMER_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFLongformerForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLongformerForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLongformerForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLongformerForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLongformerForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLongformerModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLongformerPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLongformerSelfAttention(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_LXMERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFLxmertForPreTraining(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLxmertMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLxmertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLxmertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFLxmertVisualFeatureEncoder(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMarianModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMarianMTModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMarianPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMBartForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMBartModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMBartPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_MOBILEBERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFMobileBertForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileBertForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileBertForNextSentencePrediction(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileBertForPreTraining(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileBertForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileBertForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileBertForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileBertMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileBertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileBertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_MOBILEVIT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFMobileViTForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileViTForSemanticSegmentation(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileViTModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMobileViTPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_MPNET_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFMPNetForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMPNetForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMPNetForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMPNetForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMPNetForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMPNetMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMPNetModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMPNetPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMT5EncoderModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMT5ForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFMT5Model(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_OPENAI_GPT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFOpenAIGPTDoubleHeadsModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFOpenAIGPTForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFOpenAIGPTLMHeadModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFOpenAIGPTMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFOpenAIGPTModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFOpenAIGPTPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFOPTForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFOPTModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFOPTPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFPegasusForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFPegasusModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFPegasusPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRagModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRagPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRagSequenceForGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRagTokenForGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_REGNET_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFRegNetForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRegNetModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRegNetPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_REMBERT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFRemBertForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRemBertForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRemBertForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRemBertForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRemBertForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRemBertForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRemBertLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRemBertModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRemBertPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_RESNET_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFResNetForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFResNetModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFResNetPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_ROBERTA_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFRobertaForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_ROBERTA_PRELAYERNORM_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFRobertaPreLayerNormForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaPreLayerNormForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaPreLayerNormForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaPreLayerNormForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaPreLayerNormForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaPreLayerNormForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaPreLayerNormMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaPreLayerNormModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRobertaPreLayerNormPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_ROFORMER_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFRoFormerForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRoFormerForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRoFormerForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRoFormerForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRoFormerForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRoFormerForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRoFormerLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRoFormerModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFRoFormerPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_SAM_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFSamModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSamPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_SEGFORMER_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFSegformerDecodeHead(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSegformerForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSegformerForSemanticSegmentation(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSegformerModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSegformerPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_SPEECH_TO_TEXT_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFSpeech2TextForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSpeech2TextModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSpeech2TextPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_SWIN_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFSwinForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSwinForMaskedImageModeling(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSwinModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFSwinPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_T5_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFT5EncoderModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFT5ForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFT5Model(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFT5PreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_TAPAS_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFTapasForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTapasForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTapasForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTapasModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTapasPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_TRANSFO_XL_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFAdaptiveEmbedding(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTransfoXLForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTransfoXLLMHeadModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTransfoXLMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTransfoXLModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFTransfoXLPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFVisionEncoderDecoderModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFVisionTextDualEncoderModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFViTForImageClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFViTModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFViTPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFViTMAEForPreTraining(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFViTMAEModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFViTMAEPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_WAV_2_VEC_2_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFWav2Vec2ForCTC(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFWav2Vec2ForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFWav2Vec2Model(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFWav2Vec2PreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_WHISPER_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFWhisperForConditionalGeneration(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFWhisperModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFWhisperPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_XGLM_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFXGLMForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXGLMModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXGLMPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_XLM_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFXLMForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMForQuestionAnsweringSimple(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMWithLMHeadModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_XLM_ROBERTA_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFXLMRobertaForCausalLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMRobertaForMaskedLM(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMRobertaForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMRobertaForQuestionAnswering(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMRobertaForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMRobertaForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMRobertaModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLMRobertaPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


TF_XLNET_PRETRAINED_MODEL_ARCHIVE_LIST = None


class TFXLNetForMultipleChoice(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLNetForQuestionAnsweringSimple(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLNetForSequenceClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLNetForTokenClassification(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLNetLMHeadModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLNetMainLayer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLNetModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class TFXLNetPreTrainedModel(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class AdamWeightDecay(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class GradientAccumulator(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


class WarmUp(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])


def create_optimizer(*args, **kwargs):
    requires_backends(create_optimizer, ["tf"])


class TFTrainer(metaclass=DummyObject):
    _backends = ["tf"]

    def __init__(self, *args, **kwargs):
        requires_backends(self, ["tf"])
