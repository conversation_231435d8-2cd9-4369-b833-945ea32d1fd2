# 本地即時翻譯配置文件 - 系統音頻版本
# 此配置用於捕獲系統音頻 (如 YouTube 影片)

# 音頻設置
audio:
  sample_rate: 16000
  chunk_size: 1024
  channels: 1
  device_index: null  # 設為輸出設備編號以捕獲系統音頻
  input_timeout: 1.0  # 音頻輸入超時時間（秒）
  capture_system_audio: true  # 啟用系統音頻捕獲

# Whisper 設置
whisper:
  model_name: "large-v3"
  language: "ja"  # 日語
  task: "transcribe"
  temperature: 0.0
  no_speech_threshold: 0.1  # 降低閾值以捕獲更多音頻
  condition_on_previous_text: true
  initial_prompt: "以下是日語對話的轉錄。"

# LLM 翻譯設置
llm:
  model_name: "facebook/nllb-200-distilled-600M"  # 多語言翻譯模型
  max_length: 512
  temperature: 0.7
  do_sample: true
  pad_token_id: 50256
  
# 翻譯提示詞
translation:
  system_prompt: "你是一位專業翻譯員。請將日文字幕翻譯成正體中文。並且盡可能貼近一些日常的用語，請勿做任何額外解釋、註釋，直接翻譯出來就好。"
  source_language: "日語"
  target_language: "繁體中文"

# 輸出設置
output:
  show_timestamps: true
  timestamp_format: "%H:%M:%S"
  show_original: true
  show_translation: true
  log_to_file: false
  log_file: "translation_log.txt"

# 性能設置
performance:
  max_audio_length: 30  # 最大音頻長度（秒）
  processing_interval: 0.3  # 處理間隔（秒）- 更快的處理
  buffer_size: 3  # 音頻緩衝區大小（秒）- 更小的緩衝
