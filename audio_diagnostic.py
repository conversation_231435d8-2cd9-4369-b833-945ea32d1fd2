#!/usr/bin/env python3
"""
音頻診斷工具 - 幫助診斷和修正音頻捕獲問題
"""

import sys
import time
import numpy as np
from pathlib import Path

# 添加 src 目錄到 Python 路徑
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_audio_devices():
    """測試所有音頻設備"""
    print("=" * 60)
    print("音頻設備診斷工具")
    print("=" * 60)
    
    try:
        import sounddevice as sd
        
        print("1. 顯示所有音頻設備:")
        print("-" * 40)
        
        devices = sd.query_devices()
        input_devices = []
        output_devices = []
        
        for i, device in enumerate(devices):
            hostapi_name = sd.query_hostapis(device['hostapi'])['name']
            
            if device['max_input_channels'] > 0:
                input_devices.append((i, device, hostapi_name))
                print(f"輸入設備 {i}: {device['name']}")
                print(f"  - 主機API: {hostapi_name}")
                print(f"  - 輸入通道: {device['max_input_channels']}")
                print(f"  - 預設採樣率: {device['default_samplerate']}")
                print()
            
            if device['max_output_channels'] > 0:
                output_devices.append((i, device, hostapi_name))
                print(f"輸出設備 {i}: {device['name']}")
                print(f"  - 主機API: {hostapi_name}")
                print(f"  - 輸出通道: {device['max_output_channels']}")
                print(f"  - 預設採樣率: {device['default_samplerate']}")
                print(f"  - 支援 loopback: {'是' if hostapi_name == 'Windows WASAPI' else '否'}")
                print()
        
        return input_devices, output_devices
        
    except Exception as e:
        print(f"❌ 設備檢測失敗: {e}")
        return [], []

def test_system_audio_capture(device_index):
    """測試特定設備的系統音頻捕獲"""
    print(f"\n測試設備 {device_index} 的系統音頻捕獲...")
    print("請確保有音頻正在播放 (如 YouTube)")
    print("測試將持續 10 秒...")
    
    try:
        import sounddevice as sd
        
        audio_data = []
        max_volume = 0.0
        
        def audio_callback(indata, frames, time, status):
            nonlocal max_volume
            if status:
                print(f"音頻狀態: {status}")
            
            # 轉換為一維數組
            data = indata[:, 0] if indata.shape[1] > 0 else indata.flatten()
            volume = np.max(np.abs(data))
            max_volume = max(max_volume, volume)
            
            if volume > 0.001:
                audio_data.append(data.copy())
                print(f"檢測到音頻! 音量: {volume:.4f}")
        
        # 嘗試使用 WASAPI loopback
        try:
            with sd.InputStream(
                samplerate=16000,
                channels=1,
                device=device_index,
                callback=audio_callback,
                blocksize=1024,
                dtype=np.float32,
                extra_settings=sd.WasapiSettings(exclusive=False, auto_convert=True)
            ) as stream:
                print("✓ 使用 WASAPI loopback 模式")
                time.sleep(10)
        except Exception as wasapi_error:
            print(f"WASAPI loopback 失敗: {wasapi_error}")
            print("嘗試標準模式...")
            
            with sd.InputStream(
                samplerate=16000,
                channels=1,
                device=device_index,
                callback=audio_callback,
                blocksize=1024,
                dtype=np.float32
            ) as stream:
                print("✓ 使用標準模式")
                time.sleep(10)
        
        print(f"\n測試結果:")
        print(f"- 最大音量: {max_volume:.4f}")
        print(f"- 音頻片段數: {len(audio_data)}")
        
        if len(audio_data) > 0:
            print("✅ 成功捕獲音頻!")
            return True
        else:
            print("❌ 未檢測到音頻")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def check_windows_audio_settings():
    """檢查 Windows 音頻設置"""
    print("\n檢查 Windows 音頻設置...")
    print("-" * 40)
    
    print("請檢查以下設置:")
    print("1. Windows 音效設定:")
    print("   - 右鍵點擊音量圖標 → 開啟音效設定")
    print("   - 確保正確的播放設備已啟用")
    print()
    print("2. 立體聲混音 (如果可用):")
    print("   - 右鍵點擊音量圖標 → 音效")
    print("   - 錄製標籤 → 右鍵空白處 → 顯示已停用的裝置")
    print("   - 啟用立體聲混音")
    print()
    print("3. 應用程式音量:")
    print("   - 確保 YouTube 等應用程式音量不是靜音")
    print("   - 系統音量可以設為 0，但應用程式音量要有聲音")

def main():
    """主函數"""
    input_devices, output_devices = test_audio_devices()
    
    if not output_devices:
        print("❌ 未找到輸出設備")
        return
    
    print("\n" + "=" * 60)
    print("系統音頻捕獲測試")
    print("=" * 60)
    
    print("建議測試的輸出設備 (用於系統音頻捕獲):")
    wasapi_devices = []
    for device_id, device, hostapi in output_devices:
        if hostapi == "Windows WASAPI":
            wasapi_devices.append(device_id)
            print(f"  設備 {device_id}: {device['name']}")
    
    if not wasapi_devices:
        print("⚠️  未找到 WASAPI 設備，系統音頻捕獲可能不可用")
        return
    
    print(f"\n推薦測試設備: {wasapi_devices[0]} (第一個 WASAPI 設備)")
    
    choice = input(f"\n輸入要測試的設備編號 (預設: {wasapi_devices[0]}): ").strip()
    
    if choice.isdigit():
        device_index = int(choice)
    else:
        device_index = wasapi_devices[0]
    
    print(f"\n開始測試設備 {device_index}...")
    print("請在另一個視窗播放 YouTube 影片或音樂")
    input("準備好後按 Enter 開始測試...")
    
    success = test_system_audio_capture(device_index)
    
    if success:
        print(f"\n✅ 設備 {device_index} 可以正常捕獲系統音頻!")
        print(f"請在配置文件中使用:")
        print(f"audio:")
        print(f"  device_index: {device_index}")
        print(f"  capture_system_audio: true")
    else:
        print(f"\n❌ 設備 {device_index} 無法捕獲系統音頻")
        check_windows_audio_settings()
        
        print("\n故障排除建議:")
        print("1. 嘗試其他 WASAPI 設備")
        print("2. 檢查 Windows 音效設定")
        print("3. 確保應用程式有音頻輸出")
        print("4. 嘗試啟用立體聲混音")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n診斷已取消")
    except Exception as e:
        print(f"\n❌ 診斷過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
